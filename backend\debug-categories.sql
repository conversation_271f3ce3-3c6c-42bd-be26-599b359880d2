-- Script para debugar o problema das categorias

-- 1. Verificar todas as categorias do usuário 5
SELECT 'CATEGORIAS DO USUÁRIO 5:' as info;
SELECT id, name, transaction_type, color, user_id 
FROM finances_categories 
WHERE user_id = 5
ORDER BY id;

-- 2. <PERSON>eri<PERSON><PERSON> todas as transações de 2025 do usuário 5
SELECT 'TRANSAÇÕES DE 2025 DO USUÁRIO 5:' as info;
SELECT id, transaction_type, category_id, description, amount, transaction_date, is_saving
FROM finances 
WHERE user_id = 5 AND YEAR(transaction_date) = 2025
ORDER BY transaction_date
LIMIT 10;

-- 3. Verificar se há transações com category_id que não existem
SELECT 'TRANSAÇÕES COM CATEGORY_ID INVÁLIDO:' as info;
SELECT f.id, f.category_id, f.description, f.amount, fc.name as category_name
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND YEAR(f.transaction_date) = 2025
  AND f.category_id IS NOT NULL 
  AND fc.id IS NULL;

-- 4. Simular a query do backend para gastos por categoria
SELECT 'SIMULAÇÃO DA QUERY DO BACKEND:' as info;
SELECT 
    fc.name as category,
    SUM(f.amount) as amount,
    COUNT(f.id) as count,
    fc.id as category_id
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND f.transaction_type = 'expense'
  AND YEAR(f.transaction_date) = 2025
GROUP BY fc.id, fc.name
ORDER BY amount DESC;

-- 5. Verificar transações de gastos sem categoria
SELECT 'GASTOS SEM CATEGORIA:' as info;
SELECT f.id, f.description, f.amount, f.category_id, f.transaction_date
FROM finances f
WHERE f.user_id = 5 
  AND f.transaction_type = 'expense'
  AND YEAR(f.transaction_date) = 2025
  AND f.category_id IS NULL;

-- 6. Verificar transações de gastos com categoria
SELECT 'GASTOS COM CATEGORIA:' as info;
SELECT f.id, f.description, f.amount, f.category_id, fc.name as category_name, f.transaction_date
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND f.transaction_type = 'expense'
  AND YEAR(f.transaction_date) = 2025
  AND f.category_id IS NOT NULL
LIMIT 10;
