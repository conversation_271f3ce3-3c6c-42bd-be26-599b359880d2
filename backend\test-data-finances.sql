-- Script para inserir dados de teste para finanças anuais
-- Execute este SQL no phpMyAdmin para testar a funcionalidade

-- Inserir categorias de finanças de teste (se não existirem)
INSERT IGNORE INTO finances_categories (name, transaction_type, color, user_id, created_at, updated_at) VALUES
('Salário', 'income', '#4CAF50', 5, NOW(), NOW()),
('Freelance', 'income', '#8BC34A', 5, NOW(), NOW()),
('Investimentos', 'income', '#CDDC39', 5, NOW(), NOW()),
('Alimentação', 'expense', '#F44336', 5, NOW(), NOW()),
('Transporte', 'expense', '#FF9800', 5, NOW(), NOW()),
('Moradia', 'expense', '#9C27B0', 5, NOW(), NOW()),
('Saúde', 'expense', '#E91E63', 5, NOW(), NOW()),
('Lazer', 'expense', '#3F51B5', 5, NOW(), NOW());

-- Inserir transações de teste para o ano atual (2024)
-- Janeiro - Receitas: R$ 6.500, Gastos: R$ 2.300, Economia: R$ 500
INSERT INTO finances (transaction_type, category_id, description, amount, transaction_date, user_id, is_saving, created_at, updated_at) VALUES
('income', 1, 'Salário Janeiro', '5000.00', '2024-01-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Janeiro', '1500.00', '2024-01-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '800.00', '2024-01-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '300.00', '2024-01-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-01-01', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Janeiro', '500.00', '2024-01-31', 5, true, NOW(), NOW()),

-- Fevereiro - Receitas: R$ 7.000, Gastos: R$ 2.230, Economia: R$ 600
('income', 1, 'Salário Fevereiro', '5000.00', '2024-02-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Fevereiro', '2000.00', '2024-02-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '750.00', '2024-02-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '280.00', '2024-02-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-02-01', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Fevereiro', '600.00', '2024-02-29', 5, true, NOW(), NOW()),

-- Março - Receitas: R$ 5.800, Gastos: R$ 2.620, Economia: R$ 700
('income', 1, 'Salário Março', '5000.00', '2024-03-15', 5, false, NOW(), NOW()),
('income', 3, 'Dividendos', '800.00', '2024-03-10', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '900.00', '2024-03-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '320.00', '2024-03-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-03-01', 5, false, NOW(), NOW()),
('expense', 7, 'Consulta médica', '200.00', '2024-03-15', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Março', '700.00', '2024-03-31', 5, true, NOW(), NOW()),

-- Abril
('income', 1, 'Salário Abril', '5000.00', '2024-04-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Abril', '1200.00', '2024-04-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '850.00', '2024-04-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '290.00', '2024-04-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-04-01', 5, false, NOW(), NOW()),
('expense', 8, 'Cinema', '80.00', '2024-04-12', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Abril', '550.00', '2024-04-30', 5, true, NOW(), NOW()),

-- Maio
('income', 1, 'Salário Maio', '5000.00', '2024-05-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Maio', '1800.00', '2024-05-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '820.00', '2024-05-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '310.00', '2024-05-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-05-01', 5, false, NOW(), NOW()),
('expense', 8, 'Restaurante', '150.00', '2024-05-18', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Maio', '650.00', '2024-05-31', 5, true, NOW(), NOW()),

-- Junho - Receitas: R$ 7.200, Gastos: R$ 2.910, Economia: R$ 800
('income', 1, 'Salário Junho', '5000.00', '2024-06-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Junho', '2200.00', '2024-06-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '880.00', '2024-06-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '330.00', '2024-06-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-06-01', 5, false, NOW(), NOW()),
('expense', 8, 'Viagem', '500.00', '2024-06-22', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Junho', '800.00', '2024-06-30', 5, true, NOW(), NOW()),

-- Julho - Receitas: R$ 5.500, Gastos: R$ 2.400, Economia: R$ 600
('income', 1, 'Salário Julho', '5000.00', '2024-07-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Julho', '500.00', '2024-07-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '850.00', '2024-07-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '350.00', '2024-07-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-07-01', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Julho', '600.00', '2024-07-31', 5, true, NOW(), NOW()),

-- Agosto - Receitas: R$ 6.200, Gastos: R$ 2.650, Economia: R$ 750
('income', 1, 'Salário Agosto', '5000.00', '2024-08-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Agosto', '1200.00', '2024-08-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '900.00', '2024-08-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '350.00', '2024-08-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-08-01', 5, false, NOW(), NOW()),
('expense', 7, 'Dentista', '200.00', '2024-08-15', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Agosto', '750.00', '2024-08-31', 5, true, NOW(), NOW()),

-- Setembro - Receitas: R$ 5.800, Gastos: R$ 2.550, Economia: R$ 650
('income', 1, 'Salário Setembro', '5000.00', '2024-09-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Setembro', '800.00', '2024-09-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '800.00', '2024-09-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '300.00', '2024-09-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-09-01', 5, false, NOW(), NOW()),
('expense', 8, 'Academia', '250.00', '2024-09-15', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Setembro', '650.00', '2024-09-30', 5, true, NOW(), NOW()),

-- Outubro - Receitas: R$ 6.500, Gastos: R$ 2.800, Economia: R$ 900
('income', 1, 'Salário Outubro', '5000.00', '2024-10-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Outubro', '1500.00', '2024-10-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '950.00', '2024-10-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '350.00', '2024-10-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-10-01', 5, false, NOW(), NOW()),
('expense', 8, 'Roupas', '300.00', '2024-10-15', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Outubro', '900.00', '2024-10-31', 5, true, NOW(), NOW()),

-- Novembro - Receitas: R$ 7.500, Gastos: R$ 3.200, Economia: R$ 1000
('income', 1, 'Salário Novembro', '5000.00', '2024-11-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Novembro', '2000.00', '2024-11-20', 5, false, NOW(), NOW()),
('income', 2, 'Bônus', '500.00', '2024-11-25', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '1000.00', '2024-11-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '400.00', '2024-11-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-11-01', 5, false, NOW(), NOW()),
('expense', 8, 'Black Friday', '600.00', '2024-11-29', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Novembro', '1000.00', '2024-11-30', 5, true, NOW(), NOW()),

-- Dezembro - Receitas: R$ 8.000, Gastos: R$ 4.000, Economia: R$ 1200
('income', 1, 'Salário Dezembro', '5000.00', '2024-12-15', 5, false, NOW(), NOW()),
('income', 1, '13º Salário', '2500.00', '2024-12-20', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Dezembro', '500.00', '2024-12-10', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '1200.00', '2024-12-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '400.00', '2024-12-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2024-12-01', 5, false, NOW(), NOW()),
('expense', 8, 'Presentes Natal', '800.00', '2024-12-20', 5, false, NOW(), NOW()),
('expense', 8, 'Ceia Natal', '400.00', '2024-12-24', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Dezembro', '1200.00', '2024-12-31', 5, true, NOW(), NOW());

-- Inserir meta anual de teste (Total de economia esperado: ~R$ 9.000)
INSERT INTO annual_goals (user_id, goal_amount, year, created_at, updated_at) VALUES
(5, '8000.00', 2024, NOW(), NOW())
ON DUPLICATE KEY UPDATE
goal_amount = '8000.00',
updated_at = NOW();

-- Verificar se os dados foram inseridos
SELECT 'Categorias inseridas:' as info;
SELECT COUNT(*) as total_categories FROM finances_categories WHERE user_id = 5;

SELECT 'Transações inseridas:' as info;
SELECT COUNT(*) as total_transactions FROM finances WHERE user_id = 5;

SELECT 'Meta anual inserida:' as info;
SELECT * FROM annual_goals WHERE user_id = 5 AND year = 2024;

-- Resumo por mês
SELECT 
    MONTH(transaction_date) as mes,
    MONTHNAME(transaction_date) as nome_mes,
    SUM(CASE WHEN transaction_type = 'income' THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END) as receitas,
    SUM(CASE WHEN transaction_type = 'expense' AND is_saving = false THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END) as gastos,
    SUM(CASE WHEN is_saving = true THEN CAST(amount AS DECIMAL(10,2)) ELSE 0 END) as economia
FROM finances 
WHERE user_id = 5 AND YEAR(transaction_date) = 2024
GROUP BY MONTH(transaction_date), MONTHNAME(transaction_date)
ORDER BY MONTH(transaction_date);
