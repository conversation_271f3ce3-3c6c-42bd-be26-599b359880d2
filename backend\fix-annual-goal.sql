-- <PERSON><PERSON>t para corrigir o problema da meta anual

-- <PERSON><PERSON>r tabela annual_goals se não existir
CREATE TABLE IF NOT EXISTS annual_goals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    goal_amount DECIMAL(10,2) NOT NULL,
    year INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_year (user_id, year)
);

-- Inserir meta anual para o usuário de teste
INSERT INTO annual_goals (user_id, goal_amount, year, created_at, updated_at) VALUES
(5, '8000.00', 2024, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
goal_amount = '8000.00',
updated_at = NOW();

-- Verificar se foi inserido
SELECT * FROM annual_goals WHERE user_id = 5 AND year = 2024;

-- Verificar se há transações para o usuário
SELECT 
    COUNT(*) as total_transactions,
    MIN(transaction_date) as primeira_transacao,
    MAX(transaction_date) as ultima_transacao
FROM finances 
WHERE user_id = 5 AND YEAR(transaction_date) = 2024;
