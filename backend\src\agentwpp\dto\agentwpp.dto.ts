import { IsNotEmpty, IsString, IsOptional, IsEnum, IsDateString, IsNumber, IsBoolean, IsDecimal, IsPhoneNumber, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Length, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { PaginationDto } from '../../common/dto/common-response.dto';

// Base DTO com telefone para identificação do usuário
export class BaseAgentWppDto {
  @IsNotEmpty({ message: 'Telefone é obrigatório' })
  @IsString({ message: 'Telefone deve ser uma string' })
  @Transform(({ value }) => value?.replace(/\s+/g, '').replace(/[^0-9+]/g, ''))
  phone: string;
}

// DTO para paginação específica do AgentWPP
export class AgentWppPaginationDto extends PaginationDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsOptional()
  @IsNumber()
  categoryId?: number;
}

// DTOs para Tarefas
export class CreateTaskAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsEnum(['appointment', 'task'])
  task_type: 'appointment' | 'task';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  task_date?: string;
}

export class UpdateTaskAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsEnum(['appointment', 'task'])
  task_type?: 'appointment' | 'task';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  task_date?: string;
}

export class CreateTaskCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

// DTOs para Finanças
export class CreateFinanceAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  transaction_type: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  @IsDecimal({ decimal_digits: '0,2' })
  amount: string;

  @IsNotEmpty()
  @IsDateString()
  transaction_date: string;
}

export class UpdateFinanceAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsEnum(['income', 'expense'])
  transaction_type?: 'income' | 'expense';

  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsBoolean()
  is_saving?: boolean;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  @IsDecimal({ decimal_digits: '0,2' })
  amount?: string;

  @IsOptional()
  @IsDateString()
  transaction_date?: string;
}

export class CreateFinanceCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsEnum(['income', 'expense'])
  transaction_type: 'income' | 'expense';

  @IsOptional()
  @IsString()
  color?: string;
}

// DTOs para Ideias
export class CreateIdeaAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsBoolean()
  is_favorite?: boolean;
}

export class UpdateIdeaAgentWppDto extends BaseAgentWppDto {
  @IsOptional()
  @IsNumber()
  category_id?: number;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  content?: string;

  @IsOptional()
  @IsBoolean()
  is_favorite?: boolean;
}

export class CreateIdeaCategoryAgentWppDto extends BaseAgentWppDto {
  @IsNotEmpty()
  @IsString()
  name: string;
}

// DTOs de resposta
export class CheckIntegrationResponseDto {
  hasIntegration: boolean;
  status?: 'pending' | 'active' | 'inactive';
  userId?: number;
}

export class AgentWppDashboardResponseDto {
  user: {
    name: string;
    timezone: string;
  };
  tasks: {
    completed: number;
    total: number;
  };
  finances: {
    spent: number;
    budget: number;
    income: number;
    savings: number;
  };
  ideas: {
    today: number;
    total: number;
    favorites: number;
  };
}
