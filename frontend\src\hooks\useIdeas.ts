import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import {
  IdeaListResponseDto,
  IdeaResponseDto,
  CreateIdeaDto,
  UpdateIdeaDto,
  IdeaCategoryResponseDto,
  CreateIdeaCategoryDto,
  UpdateIdeaCategoryDto,
  PaginationParams,
} from '../types/api';

// Hook para listar ideas
export const useIdeas = (params?: PaginationParams) => {
  return useQuery({
    queryKey: ['ideas', params],
    queryFn: async (): Promise<IdeaListResponseDto> => {
      const searchParams = new URLSearchParams();
      if (params?.page) searchParams.append('page', params.page.toString());
      if (params?.limit) searchParams.append('limit', params.limit.toString());
      
      const response = await authenticatedApi.get(`ideas?${searchParams.toString()}`);
      const responseData = await response.json();
      
      // Handle nested response structure: response.data.data or response.data or response
      return responseData.data?.data || responseData.data || responseData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
  });
};

// Hook para buscar uma idea específica
export const useIdea = (id: number) => {
  return useQuery({
    queryKey: ['ideas', id],
    queryFn: async (): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.get(`ideas/${id}`);
      return response.json();
    },
    enabled: !!id,
  });
};

// Hook para criar idea
export const useCreateIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateIdeaDto): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.post('ideas', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para atualizar idea
export const useUpdateIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateIdeaDto }): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.put(`ideas/${id}`, { json: data });
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da idea específica
      queryClient.setQueryData(['ideas', data.id], data);
    },
  });
};

// Hook para deletar idea
export const useDeleteIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`ideas/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });
};

// Hook para marcar/desmarcar idea como favorita
export const useToggleFavoriteIdea = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<IdeaResponseDto> => {
      const response = await authenticatedApi.patch(`ideas/${id}/favorite`);
      return response.json();
    },
    onSuccess: (data) => {
      // Invalidar cache das ideas e dashboard
      queryClient.invalidateQueries({ queryKey: ['ideas'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
      // Atualizar cache da idea específica
      queryClient.setQueryData(['ideas', data.id], data);
    },
  });
};

// Hook para listar categorias de ideas
export const useIdeaCategories = () => {
  return useQuery({
    queryKey: ['idea-categories'],
    queryFn: async (): Promise<IdeaCategoryResponseDto[]> => {
      const response = await authenticatedApi.get('ideas/categories');
      return response.json();
    },
    staleTime: 10 * 60 * 1000, // 10 minutos (categorias mudam menos)
  });
};

// Hook para criar categoria de idea
export const useCreateIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateIdeaCategoryDto): Promise<IdeaCategoryResponseDto> => {
      const response = await authenticatedApi.post('ideas/categories', { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};

// Hook para atualizar categoria de idea
export const useUpdateIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateIdeaCategoryDto }): Promise<IdeaCategoryResponseDto> => {
      const response = await authenticatedApi.put(`ideas/categories/${id}`, { json: data });
      return response.json();
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};

// Hook para deletar categoria de idea
export const useDeleteIdeaCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: number): Promise<void> => {
      await authenticatedApi.delete(`ideas/categories/${id}`);
    },
    onSuccess: () => {
      // Invalidar cache das categorias
      queryClient.invalidateQueries({ queryKey: ['idea-categories'] });
    },
  });
};
