export const SERVER_NAME = "agentwpp-mcp-server";
export const SERVER_DESCRIPTION = "MCP server for AgentWPP - WhatsApp integration for tasks, finances, and ideas management.";
export const SERVER_VERSION = "1.0.0";

// API Configuration
export const API_URL = process.env.API_URL || "http://localhost:3000";
export const N8N_API_KEY = process.env.N8N_API_KEY || "";

// Error Messages
export const ERROR_PHONE_REQUIRED = "Phone number is required";
export const ERROR_API_KEY_MISSING = "N8N API key is not configured";
export const ERROR_API_REQUEST_FAILED = "API request failed";
export const ERROR_INVALID_RESPONSE = "Invalid API response";
export const ERROR_UNAUTHORIZED = "Unauthorized request";
export const ERROR_NOT_FOUND = "Resource not found";
export const ERROR_VALIDATION_FAILED = "Validation failed";
export const ERROR_NETWORK_ERROR = "Network error occurred";
export const ERROR_TIMEOUT = "Request timeout";
export const ERROR_SERVER_ERROR = "Server error";
export const ERROR_BAD_REQUEST = "Bad request";
export const ERROR_FORBIDDEN = "Forbidden access";
export const ERROR_INTERNAL_ERROR = "Internal server error";
export const ERROR_PARSE_JSON = "Failed to parse JSON response";
export const ERROR_INVALID_ENDPOINT = "Invalid API endpoint";
export const ERROR_MISSING_PARAMETERS = "Missing required parameters";

// HTTP Headers
export const HEADERS = {
  'Content-Type': 'application/json',
  // 'Authorization': `Bearer ${N8N_API_KEY}`,
  'x-api-key': N8N_API_KEY,
  'User-Agent': 'MCP-AgentWPP-Server/1.0.0'
};