import { Injectable, Inject } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { DATABASE_CONNECTION } from '../database/database.provider';
import { BaseRepository } from './base.repository';
import { IFinanceCategoryRepository } from './interfaces/finance-category.repository.interface';
import { CreateFinanceCategoryDto } from '../finances/dto/create-finance-category.dto';
import { UpdateFinanceCategoryDto } from '../finances/dto/update-finance-category.dto';
import { FinanceCategoryResponseDto } from '../finances/dto/finance-category-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class FinanceCategoryRepository extends BaseRepository<FinanceCategoryResponseDto, CreateFinanceCategoryDto, UpdateFinanceCategoryDto> implements IFinanceCategoryRepository {
  
  constructor(@Inject(DATABASE_CONNECTION) db: Kysely<Database>) {
    super(db, 'FinanceCategoryRepository');
  }

  get tableName(): keyof Database {
    return 'finances_categories';
  }

  get entityName(): string {
    return 'categoria financeira';
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): FinanceCategoryResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      transaction_type: entity.transaction_type,
      color: entity.color || undefined,
      user_id: entity.user_id,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateFinanceCategoryDto, userId: number): any {
    return {
      ...dto,
      user_id: userId,
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateFinanceCategoryDto): any {
    return {
      ...dto,
      updated_at: new Date()
    };
  }

  async checkCategoryInUse(id: number, userId: number): Promise<boolean> {
    try {
      const transactionsUsingCategory = await this.db
        .selectFrom('finances')
        .select(['id'])
        .where('category_id', '=', id)
        .where('user_id', '=', userId)
        .limit(1)
        .execute();

      return transactionsUsingCategory.length > 0;
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'verificar uso da categoria', userId);
    }
  }

  async findAllOrderedByType(userId: number, userTimezone: string = 'UTC') {
    try {
      const categories = await this.db
        .selectFrom('finances_categories')
        .selectAll()
        .where('user_id', '=', userId)
        .orderBy('transaction_type', 'asc')
        .orderBy('name', 'asc')
        .execute();

      return categories.map(category => this.mapToResponseDto(category, userTimezone));
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'listar categorias ordenadas', userId);
    }
  }
}