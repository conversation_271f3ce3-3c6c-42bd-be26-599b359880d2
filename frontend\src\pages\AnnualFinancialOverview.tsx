import React, { useState } from 'react';
import { <PERSON>Lef<PERSON>, Filter, X, Download, Target, TrendingUp, TrendingDown, Calendar, BarChart3, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import {
  useFinances,
  useFinanceCategories,
  useAnnualFinancialSummary,
  useAnnualComparison,
  useAnnualCategoryDistribution,
  useExportAnnualReport
} from '../hooks/useFinances';
import { useSetAnnualGoal } from '../hooks/useAnnualGoal';
import { useCategoryDistribution } from '../hooks/usePercentageCalculations';
import { formatPercentage, getTrendColor } from '../utils/percentageCalculator';

interface MonthData {
  month: string;
  monthNumber: number;
  income: number;
  expenses: number;
  savings: number;
  balance: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
  percentage: number;
}

const AnnualFinancialOverview: React.FC = () => {
  const navigate = useNavigate();
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string>('all');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
  const [isGoalsModalOpen, setIsGoalsModalOpen] = useState(false);
  const [annualGoalInput, setAnnualGoalInput] = useState('');

  const currentYear = new Date().getFullYear();
  const availableYears = Array.from({ length: 5 }, (_, i) => currentYear - i);

  // Fetch data from API
  const { data: annualSummaryData, isLoading: isLoadingAnnualSummary, error: annualSummaryError } = useAnnualFinancialSummary(selectedYear);
  const { data: categoriesData, isLoading: isLoadingCategories } = useFinanceCategories();
  const { data: categoryDistributionData, isLoading: isLoadingCategoryDistribution } = useAnnualCategoryDistribution(selectedYear);
  const { data: annualComparisonData, isLoading: isLoadingComparison } = useAnnualComparison(selectedYear, selectedYear - 1);
  const exportReportMutation = useExportAnnualReport();

  // Hook para gerenciar meta anual via backend
  const setAnnualGoalMutation = useSetAnnualGoal();

  // Loading state
  if (isLoadingAnnualSummary || isLoadingCategories) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dados anuais...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (annualSummaryError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados financeiros. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  // Check if we have real data from API with all required fields
  const hasRealData = !!annualSummaryData &&
                      Array.isArray(annualSummaryData.monthlyData) &&
                      annualSummaryData.monthlyData.length > 0;

  // Only use real data - no fallback to avoid masking issues
  if (!hasRealData) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/finances')}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <h1 className="text-3xl font-bold text-gray-900">Visão Anual {selectedYear}</h1>
            </div>
          </div>

          {isLoadingAnnualSummary ? (
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Carregando dados anuais...</p>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-sm p-8 text-center">
              <AlertCircle size={48} className="text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Nenhum dado encontrado</h3>
              <p className="text-gray-600 mb-4">
                Não há transações financeiras registradas para o ano de {selectedYear}.
              </p>
              <p className="text-sm text-gray-500">
                Adicione algumas transações na página de finanças para ver o resumo anual.
              </p>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Extract data with safe fallbacks
  const monthlyData = annualSummaryData.monthlyData || [];
  const totals = annualSummaryData.totals || { income: 0, expenses: 0, savings: 0, balance: 0 };
  const averages = annualSummaryData.averages || { monthlyIncome: 0, monthlyExpenses: 0, monthlySavings: 0 };
  const metrics = annualSummaryData.metrics || { savingsRate: 0, goalAmount: 0, goalProgress: 0 };
  const highlights = annualSummaryData.highlights || {
    bestSavingsMonth: { month: 'N/A', savings: 0 },
    worstSavingsMonth: { month: 'N/A', savings: 0 },
    highestIncomeMonth: { month: 'N/A', income: 0 },
    highestExpenseMonth: { month: 'N/A', expenses: 0 }
  };


  // Usar meta do backend (já considera meta manual vs dinâmica)
  const finalMetrics = {
    ...metrics,
    goalAmount: metrics.goalAmount,
    goalProgress: metrics.goalProgress
  };

  // Extract highlights data with fallbacks
  const bestSavingsMonth = highlights?.bestSavingsMonth || { month: 'N/A', savings: 0 };
  const worstSavingsMonth = highlights?.worstSavingsMonth || { month: 'N/A', savings: 0 };
  const highestIncomeMonth = highlights?.highestIncomeMonth || { month: 'N/A', income: 0 };
  const highestExpenseMonth = highlights?.highestExpenseMonth || { month: 'N/A', expenses: 0 };

  // Use real category data from API
  const categoryData: CategoryData[] = categoryDistributionData || [];

  // Filter data based on selections
  const filteredData = monthlyData.filter(month => {
    if (selectedMonth !== 'all' && month.month !== selectedMonth) return false;
    return true;
  });

  const handleExportReport = () => {
    // Mock export functionality
    const reportData = {
      year: selectedYear,
      totalIncome: totals.income,
      totalExpenses: totals.expenses,
      totalSavings: totals.savings,
      savingsRate: finalMetrics.savingsRate,
      monthlyData,
      categoryBreakdown: categoryData,
      yearComparison: annualComparisonData || []
    };

    alert('Relatório exportado com sucesso! (Funcionalidade simulada)');
  };

  const handleSetGoal = () => {
    if (!annualGoalInput || parseFloat(annualGoalInput) <= 0) {
      alert('Por favor, insira um valor válido para a meta.');
      return;
    }

    const goalAmount = parseFloat(annualGoalInput);
    
    setAnnualGoalMutation.mutate(
      { goalAmount, year: selectedYear },
      {
        onSuccess: () => {
          setIsGoalsModalOpen(false);
          setAnnualGoalInput('');
          alert('Meta anual definida com sucesso!');
        },
        onError: (error) => {
          console.error('Erro ao definir meta anual:', error);
          alert('Erro ao definir meta. Tente novamente.');
        }
      }
    );
  };

  return (
    <div className="min-h-screen bg-[#F7F7F7] pb-24 md:pb-6">
      {/* Fixed Header */}
      <div className="fixed top-0 left-0 right-0 bg-[#F7F7F7] z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <button 
              onClick={() => navigate('/finances')}
              className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            
            <h1 className="text-xl font-bold">Visão Anual</h1>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => setIsFilterOpen(true)}
                className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
              >
                <Filter size={20} className="text-gray-600" />
              </button>
              <button
                onClick={handleExportReport}
                className="w-10 h-10 bg-white rounded-full shadow-sm hover:shadow-md transition-shadow flex items-center justify-center"
              >
                <Download size={20} className="text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-6xl mx-auto px-4 pt-20 space-y-6">
        {/* Year Summary Cards - 4x4 Grid Layout */}
        <div className="grid grid-cols-2 gap-3 md:gap-4">
          {/* Top Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#4CAF50]/10 rounded-lg">
                <TrendingUp size={16} className="text-[#4CAF50]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Receitas Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#4CAF50] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totals.income)}
              </p>
              <p className="text-xs text-gray-600">
                Média: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact'
                }).format(averages.monthlyIncome)}/mês
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#FF3B30]/10 rounded-lg">
                <TrendingDown size={16} className="text-[#FF3B30]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Despesas Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#FF3B30] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totals.expenses)}
              </p>
              <p className="text-xs text-gray-600">
                Média: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact'
                }).format(averages.monthlyExpenses)}/mês
              </p>
            </div>
          </motion.div>

          {/* Bottom Row */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#B4EB00]/10 rounded-lg">
                <Target size={16} className="text-[#B4EB00]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Economias Totais</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#B4EB00] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(totals.savings)}
              </p>
              <p className="text-xs text-gray-600">
                Taxa: {finalMetrics.savingsRate.toFixed(1)}% da receita
              </p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl p-4 shadow-sm aspect-square flex flex-col justify-between"
          >
            <div className="flex items-center gap-2 mb-2">
              <div className="p-1.5 bg-[#007AFF]/10 rounded-lg">
                <BarChart3 size={16} className="text-[#007AFF]" />
              </div>
              <h3 className="text-sm font-semibold text-gray-900">Saldo Médio</h3>
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <p className="text-xl md:text-2xl font-bold text-[#007AFF] mb-1">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                  notation: 'compact',
                  compactDisplay: 'short'
                }).format(averages.monthlySavings)}
              </p>
              <p className="text-xs text-gray-600">
                Por mês
              </p>
            </div>
          </motion.div>
        </div>

        {/* Annual Goal Progress */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-[#B4EB00]/10 rounded-lg">
                <Target size={20} className="text-[#B4EB00]" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Meta Anual de Economia</h3>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setAnnualGoalInput(finalMetrics.goalAmount > 0 ? finalMetrics.goalAmount.toString() : '');
                  setIsGoalsModalOpen(true);
                }}
                className="px-4 py-2 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors text-sm font-medium"
              >
                {finalMetrics.goalAmount > 0 ? 'Editar Meta' : 'Definir Meta'}
              </button>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Progresso da Meta</span>
              <span className="font-semibold text-gray-900">
                {finalMetrics.goalAmount > 0 ? Math.round(finalMetrics.goalProgress) : 0}%
              </span>
            </div>
            
            <div className="w-full bg-gray-100 rounded-full h-4 overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${Math.min(finalMetrics.goalProgress || 0, 100)}%` }}
                transition={{ duration: 1, delay: 0.5 }}
                className="h-full bg-gradient-to-r from-[#B4EB00] to-[#9FD700] rounded-full"
              />
            </div>
            
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                Economizado: {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(totals.savings)}
              </span>
              <span className="text-gray-600">
                Meta: {finalMetrics.goalAmount > 0 ? new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(finalMetrics.goalAmount) : 'Não definida'}
              </span>
            </div>
          </div>
        </motion.div>

        {/* Main Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              Evolução Financeira Anual
            </h3>
            <div className="flex gap-2">
              <button
                onClick={() => setViewMode('chart')}
                className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                  viewMode === 'chart'
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Gráfico
              </button>
              <button
                onClick={() => setViewMode('table')}
                className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                  viewMode === 'table'
                    ? 'bg-[#B4EB00] text-gray-900'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Tabela
              </button>
            </div>
          </div>

          {viewMode === 'chart' ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={filteredData} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6C6C6C' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#6C6C6C' }}
                    tickFormatter={(value) => 
                      new Intl.NumberFormat('pt-BR', {
                        notation: 'compact',
                        compactDisplay: 'short'
                      }).format(value)
                    }
                  />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#fff',
                      border: 'none',
                      borderRadius: '12px',
                      boxShadow: '0 4px 6px -1px rgba(0,0,0,0.1)',
                      padding: '12px'
                    }}
                    formatter={(value: number, name: string) => [
                      new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(value),
                      name === 'income' ? 'Receitas' : 
                      name === 'expenses' ? 'Despesas' : 'Economias'
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="income" 
                    stroke="#4CAF50" 
                    strokeWidth={3}
                    dot={{ fill: '#4CAF50', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="expenses" 
                    stroke="#FF3B30" 
                    strokeWidth={3}
                    dot={{ fill: '#FF3B30', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="savings" 
                    stroke="#B4EB00" 
                    strokeWidth={3}
                    dot={{ fill: '#B4EB00', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-100">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Mês</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Receitas</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Despesas</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Economias</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">Saldo</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredData.map((month, index) => (
                    <tr key={month.month} className="border-b border-gray-50 hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{month.month}</td>
                      <td className="py-3 px-4 text-right text-[#4CAF50] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.income)}
                      </td>
                      <td className="py-3 px-4 text-right text-[#FF3B30] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.expenses)}
                      </td>
                      <td className="py-3 px-4 text-right text-[#B4EB00] font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.savings)}
                      </td>
                      <td className={`py-3 px-4 text-right font-medium ${
                        month.balance >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                      }`}>
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL'
                        }).format(month.balance)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Chart Legend */}
          {viewMode === 'chart' && (
            <div className="flex items-center justify-center gap-6 mt-6 text-sm">
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#4CAF50]" />
                <span className="text-gray-600">Receitas</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#FF3B30]" />
                <span className="text-gray-600">Despesas</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-3 h-3 rounded-full bg-[#B4EB00]" />
                <span className="text-gray-600">Economias</span>
              </div>
            </div>
          )}
        </motion.div>

        {/* Category Breakdown and Year Comparison */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Breakdown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white rounded-2xl p-6 shadow-sm"
          >
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">
                Principais Categorias de Gastos
              </h3>
              {categoryData.length > 0 && (
                <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                  {categoryData.length} categoria{categoryData.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            
            {isLoadingCategoryDistribution ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 text-sm">Carregando categorias...</p>
              </div>
            ) : categoryData.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 size={24} className="text-gray-400" />
                </div>
                <p className="text-gray-600 mb-2 font-medium">Nenhuma categoria encontrada</p>
                <p className="text-sm text-gray-500 max-w-sm mx-auto">
                  Adicione transações de despesa para ver a distribuição por categorias neste ano.
                </p>
              </div>
            ) : (
              <>
                {/* Pie Chart */}
                <div className="flex justify-center mb-8">
                  <ResponsiveContainer width="100%" height={220}>
                    <PieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        innerRadius={50}
                        outerRadius={90}
                        paddingAngle={2}
                        dataKey="value"
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#fff',
                          border: 'none',
                          borderRadius: '12px',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                          padding: '12px'
                        }}
                        formatter={(value: number) => [
                          new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(value),
                          'Total gasto'
                        ]}
                        labelFormatter={(label) => `📊 ${label}`}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                </div>

                {/* Category List */}
                <div className="space-y-3">
                  {categoryData.map((category, index) => (
                    <motion.div 
                      key={category.name} 
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 * index }}
                      className="flex items-center justify-between p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className="w-5 h-5 rounded-full shadow-sm"
                          style={{ backgroundColor: category.color }}
                        />
                        <div>
                          <span className="font-medium text-gray-900">{category.name}</span>
                          {category.count && (
                            <p className="text-xs text-gray-500 mt-0.5">
                              {category.count} transaç{category.count !== 1 ? 'ões' : 'ão'}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(category.value)}
                        </p>
                        <p className="text-sm font-medium" style={{ color: category.color }}>
                          {category.percentage}%
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Summary Info */}
                <div className="mt-6 p-4 bg-blue-50 rounded-xl">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Insights das Categorias</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    {categoryData.length > 0 && (
                      <>
                        <p>
                          • **{categoryData[0].name}** é sua maior categoria de gastos ({categoryData[0].percentage}%)
                        </p>
                        {categoryData.length > 2 && (
                          <p>
                            • As 3 principais categorias representam {(categoryData.slice(0, 3).reduce((sum, cat) => sum + cat.percentage, 0)).toFixed(1)}% dos seus gastos
                          </p>
                        )}
                        <p>
                          • Total analisado: {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(categoryData.reduce((sum, cat) => sum + cat.value, 0))}
                        </p>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}
          </motion.div>

          {/* Year Comparison */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white rounded-2xl p-6 shadow-sm"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-6">
              Comparativo com Ano Anterior
            </h3>
            
            {annualComparisonData && annualComparisonData.length > 0 ? (
              <div className="space-y-4">
                {annualComparisonData.map((item, index) => (
                  <div key={item.category} className="flex items-center justify-between">
                    <span className="font-medium text-gray-900">{item.category}</span>
                    <div className="flex items-center gap-2">
                      <span className={`text-sm font-medium ${
                        item.change >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                      }`}>
                        {item.change >= 0 ? '+' : ''}{item.change.toFixed(1)}%
                      </span>
                      {item.change >= 0 ? (
                        <TrendingUp size={16} className="text-[#4CAF50]" />
                      ) : (
                        <TrendingDown size={16} className="text-[#FF3B30]" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-600 mb-2">Comparação não disponível</p>
                <p className="text-sm text-gray-500">
                  Dados do ano anterior necessários para comparação.
                </p>
              </div>
            )}
          </motion.div>
        </div>

        {/* Highlights Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white rounded-2xl p-6 shadow-sm"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">
            Destaques do Ano
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-[#4CAF50]/5 rounded-xl">
              <h4 className="font-semibold text-[#4CAF50] mb-2">Melhor Mês (Economia)</h4>
              <p className="text-lg font-bold text-gray-900">{bestSavingsMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(bestSavingsMonth.savings)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#FF3B30]/5 rounded-xl">
              <h4 className="font-semibold text-[#FF3B30] mb-2">Maior Gasto</h4>
              <p className="text-lg font-bold text-gray-900">{highestExpenseMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(highestExpenseMonth.expenses)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#B4EB00]/5 rounded-xl">
              <h4 className="font-semibold text-[#B4EB00] mb-2">Maior Receita</h4>
              <p className="text-lg font-bold text-gray-900">{highestIncomeMonth.month}</p>
              <p className="text-sm text-gray-600">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL'
                }).format(highestIncomeMonth.income)}
              </p>
            </div>
            
            <div className="text-center p-4 bg-[#007AFF]/5 rounded-xl">
              <h4 className="font-semibold text-[#007AFF] mb-2">Taxa de Economia</h4>
              <p className="text-lg font-bold text-gray-900">{finalMetrics.savingsRate.toFixed(1)}%</p>
              <p className="text-sm text-gray-600">da receita total</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filter Modal */}
      <AnimatePresence>
        {isFilterOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[100]"
              onClick={() => setIsFilterOpen(false)}
            />
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-[calc(100%-2rem)] max-w-md bg-white rounded-2xl p-6 shadow-xl z-[101]"
            >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Filtros e Configurações</h2>
                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ano
                  </label>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(Number(e.target.value))}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    {availableYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mês (opcional)
                  </label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    <option value="all">Todos os meses</option>
                    {monthlyData.map(month => (
                      <option key={month.month} value={month.month}>{month.month}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria (opcional)
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent"
                  >
                    <option value="all">Todas as categorias</option>
                    {categoryData.map(category => (
                      <option key={category.name} value={category.name}>{category.name}</option>
                    ))}
                  </select>
                </div>

                <button
                  onClick={() => setIsFilterOpen(false)}
                  className="w-full px-6 py-3 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
                >
                  Aplicar Filtros
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Goals Modal */}
      <AnimatePresence>
        {isGoalsModalOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] flex items-center justify-center p-4"
              onClick={() => setIsGoalsModalOpen(false)}
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="w-full max-w-md bg-white rounded-2xl p-6 shadow-xl relative"
                onClick={(e) => e.stopPropagation()}
              >
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Definir Meta Anual</h2>
                <button
                  onClick={() => setIsGoalsModalOpen(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X size={20} className="text-gray-600" />
                </button>
              </div>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Meta de Economia para {selectedYear}
                  </label>
                  <input
                    type="number"
                    value={annualGoalInput}
                    onChange={(e) => setAnnualGoalInput(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B4EB00] focus:border-transparent text-lg"
                    placeholder={finalMetrics.goalAmount > 0 ? finalMetrics.goalAmount.toString() : '0'}
                    min="0"
                    step="100"
                  />
                  <p className="mt-2 text-sm text-gray-600">
                    {annualGoalInput && parseFloat(annualGoalInput) > 0 ? (
                      <>Valor: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(parseFloat(annualGoalInput))}</>
                    ) : (
                      'Digite o valor da sua meta anual personalizada'
                    )}
                  </p>

                  <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800 font-medium mb-1">
                      💡 Meta Dinâmica Sugerida
                    </p>
                    <p className="text-sm text-blue-700">
                      Baseada no seu histórico: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(dynamicGoalAmount)}
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      Esta meta é calculada automaticamente com base no seu histórico de economia e tendências.
                    </p>
                  </div>

                  {finalMetrics.goalAmount > 0 && (
                    <p className="mt-2 text-xs text-green-600">
                      Meta atual: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(finalMetrics.goalAmount)}
                    </p>
                  )}
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Sugestões baseadas no histórico:</h4>
                  <div className="space-y-2 text-sm">
                    <p className="text-gray-600">
                      • Meta conservadora: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totals.savings * 1.1)} (+10%)
                    </p>
                    <p className="text-gray-600">
                      • Meta moderada: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totals.savings * 1.25)} (+25%)
                    </p>
                    <p className="text-gray-600">
                      • Meta ambiciosa: {new Intl.NumberFormat('pt-BR', {
                        style: 'currency',
                        currency: 'BRL'
                      }).format(totals.savings * 1.5)} (+50%)
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <button
                    onClick={() => setIsGoalsModalOpen(false)}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleSetGoal}
                    className="flex-1 px-4 py-3 bg-[#B4EB00] text-gray-900 rounded-lg hover:bg-opacity-90 transition-colors font-medium"
                  >
                    {finalMetrics.goalAmount > 0 ? 'Atualizar Meta' : 'Definir Meta'}
                  </button>
                </div>
              </div>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AnnualFinancialOverview;