import { toZonedTime, fromZonedTime, format } from 'date-fns-tz';

export class TimezoneUtils {
  static fromUserTimezone(date: Date | string, timezone: string): Date {
    return fromZonedTime(date, timezone);
  }

  static toUserTimezone(date: Date | string, timezone: string): Date {
    return toZonedTime(date, timezone);
  }

  static formatInTimezone(
    date: Date | string, 
    timezone: string, 
    formatStr: string = 'yyyy-MM-dd HH:mm:ss'
  ): string {
    return format(toZonedTime(date, timezone), formatStr, { timeZone: timezone });
  }

  static prepareDateForDatabase(date: Date | string | null | undefined, timezone: string): Date | null {
    if (!date) return null;
    return this.fromUserTimezone(date, timezone);
  }
}

// Manter compatibilidade com código existente
export const fromUserTimezone = TimezoneUtils.fromUserTimezone;
export const toUserTimezone = TimezoneUtils.toUserTimezone;
export const formatForUser = TimezoneUtils.formatInTimezone;