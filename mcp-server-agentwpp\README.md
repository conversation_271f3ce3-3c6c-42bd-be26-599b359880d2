
# remote-mcp-server-nodejs

A Node.js implementation of a remote Model Context Protocol (MCP) server, providing both SSE and streamable HTTP transports for MCP clients.

## Features

- **Express-based API**: RESTful endpoints for MCP communication
- **SSE and Streamable HTTP**: Supports both Server-Sent Events and streamable HTTP transports
- **TypeScript**: Written in TypeScript for type safety
- **Environment Configuration**: Uses `.env` for configuration

## Project Structure

- `src/constant.ts` – Project constants (server/tool names, descriptions, error messages)
- `src/index.ts` – Main entry point, sets up Express server and MCP transports
- `src/server.ts` – Defines the MCP server and tools
- `package.json` – Project metadata, scripts, dependencies
- `tsconfig.json` – TypeScript configuration
- `.env` – Environment variables (e.g., `PORT`)

## Getting Started

### Prerequisites

- Node.js (v14 or higher recommended)
- npm or yarn

### Installation

Clone the repository and install dependencies:

```bash
git clone <your-repo-url>
cd remote-mcp-server-nodejs
npm install
```

### Configuration

Create a `.env` file in the root directory (see `.env.example` if available):

```
PORT=3000
```

### Build

```bash
npm run build
```

### Run

```bash
npm start
```

The server will start on the port specified in `.env` (default: 3000).

## API Endpoints

- `GET /sse` – Establishes an SSE connection for MCP
- `POST /messages?sessionId=...` – Sends a message to an SSE session
- `ALL /mcp` – Handles MCP requests via streamable HTTP
- `GET /` – Health check (returns welcome message)

## Example MCP Tool

The server registers a tool named `say-greeting` that returns a greeting message for the provided input.

## Using the MCP Tool

You can connect to this MCP server using any MCP-compatible client. For example, with VS Code's Model Context Protocol extension, configure `.vscode/mcp.json` as follows:

```jsonc
{
    "servers": {
        "my-mcp-server-51ba9d8e": {
            "url": "http://localhost:3000/mcp"
        }
    }
}
```

After configuration, you can invoke the `say-greeting` tool by sending a request with an input string. Example request:

```jsonc
{
  "jsonrpc": "2.0",
  "method": "say-greeting",
  "params": { "input": "World" },
  "id": 1
}
```

Example response:

```jsonc
{
  "jsonrpc": "2.0",
  "result": {
    "content": [
      { "type": "text", "text": "Hello, World!" }
    ]
  },
  "id": 1
}
```

You can use tools like `curl`, Postman, or any MCP client to interact with the server.

## Scripts

- `npm run build` – Compile TypeScript to JavaScript
- `npm run start` – Start the server
- `npm run watch` – Watch files and rebuild on changes

## Development

TypeScript source is in the `src/` directory. Compiled output is in `dist/`.

## License

MIT
