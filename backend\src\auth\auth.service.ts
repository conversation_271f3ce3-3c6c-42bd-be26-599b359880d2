import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
// import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../database.types';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  private db = db;

  constructor(
    private readonly jwtService: JwtService,
    // private readonly configService: ConfigService,
  ) {}

  async register(registerDto: RegisterDto) {
    const { name, email, password, phone, timezone } = registerDto;
    const timezoneUser = timezone || 'America/Sao_Paulo';

    const existingUser = await this.db
      .selectFrom('users')
      .selectAll()
      .where('email', '=', email)
      .executeTakeFirst();

    if (existingUser) {
      throw new Error('O usuário já existe');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const user: any = await this.db
      .insertInto('users')
      .values({
        name,
        email,
        phone: phone || null,
        password: hashedPassword,
        timezone: timezoneUser,
        created_at: new Date(),
        updated_at: new Date(),
      })
      // .returning(['id', 'email'])
      .executeTakeFirst();

    if (!user) {
      throw new Error('Erro ao adicionar o usuário');
    }

    const userId = parseInt(user.insertId);

// Criar categorias padrão para tarefas
  const defaultTaskCategories = [
    'Reunião', 'Compromisso', 'Tarefa', 'Responsabilidade', 'Agenda', 'Chamada'
  ];
  
  for (const categoryName of defaultTaskCategories) {
    await this.db
      .insertInto('tasks_categories')
      .values({
        name: categoryName,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .execute();
  }

  // Criar categorias padrão para finanças
  const defaultFinanceCategories = [
    { name: 'Fixo', type: 'income', color: 'rgb(229, 231, 235)' },
    { name: 'Variável', type: 'income', color: 'rgb(229, 231, 235)' },
    { name: 'Investimentos', type: 'income', color: 'rgb(229, 231, 235)' },
    { name: 'Outros', type: 'income', color: 'rgb(229, 231, 235)' },
    { name: 'Alimentação', type: 'expense', color: 'rgb(180, 235, 0)' },
    { name: 'Transporte', type: 'expense', color: 'rgb(33, 33, 33)' },
    { name: 'Lazer', type: 'expense', color: 'rgb(108, 108, 108)' },
    { name: 'Moradia', type: 'expense', color: 'rgb(187, 187, 187)' },
    { name: 'Outros', type: 'expense', color: 'rgb(229, 231, 235)' }
  ];
  
  for (const category of defaultFinanceCategories) {
    await this.db
      .insertInto('finances_categories')
      .values({
        name: category.name,
        transaction_type: category.type === 'expense' ? 'expense' : 'income',
        color: category.color,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .execute();
  }

  // Criar categorias para ideas
  const defaultIdeaCategories = [
    { name: 'App' },
    { name: 'Projeto pessoal' },
    { name: 'Estudo' },
    { name: 'Negócio' },
    { name: 'Outro' },
  ];

  for (const category of defaultIdeaCategories) {
    await this.db
      .insertInto('ideas_categories')
      .values({
        name: category.name,
        user_id: userId,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .execute();
  }
  
    
    return {
      status: "success",
      data: []
    };
  }

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.db
      .selectFrom('users')
      .select(['id', 'email', 'password', 'name', 'phone', 'timezone'])
      .where('email', '=', email)
      .executeTakeFirst();
    
    if (user && user.password && await bcrypt.compare(password, user.password)) {
      return user;
    }
    return null;
  }

  async login(email: string, password: string, deviceUuid?: string) {
    const user = await this.validateUser(email, password);
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const device_uuid = deviceUuid || uuidv4();
    const payload = { sub: user.id, email: user.email };
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = uuidv4();
    const expiresAt = new Date(
      Date.now() + 7 * 24 * 60 * 60 * 1000 // 7 dias
    );

    const hashedRefreshToken = await bcrypt.hash(refreshToken, 10);
    await this.db
      .insertInto('refresh_tokens')
      .values({
        user_id: parseInt(user.id.toString()),
        refresh_token: hashedRefreshToken,
        device_uuid,
        expires_at: expiresAt,
        revoked: false,
        created_at: new Date(),
        updated_at: new Date(),
      })
      .execute();

    return {
      status: "success",
      data: {
        access_token: accessToken,
        refresh_token: refreshToken,
        device_uuid,
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          phone: user.phone || null,
          timezone: user.timezone,          
        }
      }
    };
  }

  async refresh(refreshToken: string, deviceUuid: string) {
    const token = await this.db
      .selectFrom('refresh_tokens')
      .selectAll()
      .where('device_uuid', '=', deviceUuid)
      .where('revoked', '=', false)
      .where('expires_at', '>', new Date())
      .orderBy('id', 'desc')
      .executeTakeFirst();

    if (!token || !(await bcrypt.compare(refreshToken, token.refresh_token))) {
      throw new UnauthorizedException('Invalid refresh token or device UUID');
    }

    const user = await this.db
      .selectFrom('users')
      .select(['id', 'email'])
      .where('id', '=', token.user_id!)
      .executeTakeFirst();

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const payload = { sub: user.id, email: user.email };
    const newAccessToken = this.jwtService.sign(payload);
    
    // Rotação de refresh token (opcional)
    const newRefreshToken = uuidv4();
    const hashedNewRefreshToken = await bcrypt.hash(newRefreshToken, 10);
    await this.db
      .updateTable('refresh_tokens')
      .set({
        refresh_token: hashedNewRefreshToken,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      })
      .where('id', '=', token.id)
      .execute();

    return {
      status: "success",
      data: {
        access_token: newAccessToken,
        refresh_token: newRefreshToken,
        device_uuid: token.device_uuid
      }
    };
  }

  async logout(refreshToken: string, deviceUuid: string) {
    const token = await this.db
      .selectFrom('refresh_tokens')
      .selectAll()
      .where('device_uuid', '=', deviceUuid)
      .where('revoked', '=', false)
      .executeTakeFirst();

    if (token && await bcrypt.compare(refreshToken, token.refresh_token)) {
      await this.db
        .updateTable('refresh_tokens')
        .set({ revoked: true })
        .where('id', '=', token.id)
        .execute();
    }
  }
}