import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { SuccessResponseDto, ErrorResponseDto } from '../dto/common-response.dto';

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  private readonly logger = new Logger('API');

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const startTime = Date.now();
    const { method, url, body, query, params } = request;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip || 'unknown';

    // Log da requisição
    this.logger.log({
      message: 'Incoming request',
      method,
      url,
      ip,
      userAgent,
      body: this.sanitizeBody(body),
      query,
      params,
      timestamp: new Date().toISOString(),
    });

    return next.handle().pipe(
      map((data) => {
        const duration = Date.now() - startTime;
        
        // Log de sucesso
        this.logger.log({
          message: 'Request completed successfully',
          method,
          url,
          statusCode: response.statusCode,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString(),
        });

        // Se já é um SuccessResponseDto, retorna como está
        if (data instanceof SuccessResponseDto) {
          return data;
        }

        // Senão, encapsula em SuccessResponseDto
        return new SuccessResponseDto(data, 'Operation completed successfully');
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        
        // Log de erro
        this.logger.error({
          message: 'Request failed',
          method,
          url,
          error: error.message,
          stack: error.stack,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString(),
        });

        // Padronizar erro
        if (error instanceof HttpException) {
          const status = error.getStatus();
          const errorResponse = error.getResponse();
          
          return throwError(() => new HttpException(
            new ErrorResponseDto(
              error.message,
              typeof errorResponse === 'string' ? errorResponse : JSON.stringify(errorResponse),
              status
            ),
            status
          ));
        }

        // Erro não tratado
        return throwError(() => new HttpException(
          new ErrorResponseDto(
            'Internal server error',
            error.message,
            HttpStatus.INTERNAL_SERVER_ERROR
          ),
          HttpStatus.INTERNAL_SERVER_ERROR
        ));
      })
    );
  }

  private sanitizeBody(body: any): any {
    if (!body) return body;
    
    const sensitiveFields = ['password', 'token', 'api_key', 'secret'];
    const sanitized = { ...body };
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '***REDACTED***';
      }
    }
    
    return sanitized;
  }
}
