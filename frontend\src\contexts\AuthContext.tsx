import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, tokenUtils, AuthResponse } from '../lib/api';

interface User {
  id: number;
  name: string;
  email: string;
  phone?: string;
  timezone: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    timezone?: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  // Verificar autenticação ao carregar a aplicação
  useEffect(() => {
    const initAuth = async () => {
      try {
        if (tokenUtils.isAuthenticated()) {
          const userData = tokenUtils.getUser();
          if (userData) {
            setUser(userData);
          } else {
            // Se não há dados do usuário, tentar refresh
            await refreshAuth();
          }
        }
      } catch (error) {
        console.error('Erro ao inicializar autenticação:', error);
        tokenUtils.clearTokens();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    try {
      const response: any = await authApi.login({ email, password });
      
      // Handle nested response structure: response.data.data or response.data
      const authData = response.data?.data || response.data || response;
      
      tokenUtils.setTokens(authData);
      setUser(authData.user);
    } catch (error: any) {
      
      // Tratar diferentes tipos de erro
      let errorMessage = 'Erro interno do servidor. Tente novamente.';
      
      if (error.response?.status === 401) {
        errorMessage = 'Email ou senha incorretos';
      } else if (error.response?.status === 400) {
        errorMessage = 'Dados inválidos';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      console.log('Lançando erro com mensagem:', errorMessage);
      throw new Error(errorMessage);
    }
  };

  const register = async (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    timezone?: string;
  }): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Definir timezone padrão se não fornecido
      const registrationData = {
        ...userData,
        timezone: userData.timezone || 'America/Sao_Paulo',
      };
      
      const response: any = await authApi.register(registrationData);
      
      // Handle nested response structure: response.data.data or response.data
      const responseData = response.data?.data || response.data || response;
      
      alert('Conta criada com sucesso! Faça login para continuar.');
      window.location.href = '/login';
    } catch (error: any) {
      console.error('Erro no registro:', error);
      
      // Tratar diferentes tipos de erro
      if (error.response?.status === 409) {
        throw new Error('Este email já está em uso');
      } else if (error.response?.status === 400) {
        throw new Error('Dados inválidos');
      } else {
        throw new Error('Erro interno do servidor. Tente novamente.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authApi.logout();
    } catch (error) {
      console.error('Erro no logout:', error);
    } finally {
      tokenUtils.clearTokens();
      setUser(null);
      setIsLoading(false);
    }
  };

  const refreshAuth = async (): Promise<void> => {
    try {
      const response = await authApi.refreshToken();
      
      // Handle nested response structure: response.data.data or response.data
      const authData = response.data?.data || response.data || response;
      
      tokenUtils.setTokens(authData);
      setUser(authData.user);
    } catch (error) {
      console.error('Erro ao renovar token:', error);
      tokenUtils.clearTokens();
      setUser(null);
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    register,
    logout,
    refreshAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
