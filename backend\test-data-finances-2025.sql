-- Script para inserir dados de teste para finanças anuais - ANO 2025
-- Execute este SQL no phpMyAdmin para testar a funcionalidade

-- Limpar dados existentes do usuário de teste para 2025
DELETE FROM finances WHERE user_id = 5 AND YEAR(transaction_date) = 2025;

-- Inserir categorias de finanças de teste (se não existirem)
INSERT IGNORE INTO finances_categories (name, transaction_type, color, user_id, created_at, updated_at) VALUES
('Salário', 'income', '#4CAF50', 5, NOW(), NOW()),
('Freelance', 'income', '#8BC34A', 5, NOW(), NOW()),
('Investimentos', 'income', '#CDDC39', 5, NOW(), NOW()),
('Alimentação', 'expense', '#F44336', 5, NOW(), NOW()),
('Transporte', 'expense', '#FF9800', 5, NOW(), NOW()),
('Moradia', 'expense', '#9C27B0', 5, NOW(), NOW()),
('Saúde', 'expense', '#E91E63', 5, NOW(), NOW()),
('Lazer', 'expense', '#3F51B5', 5, NOW(), NOW());

-- Inserir transações de teste para o ano atual (2025)
-- Janeiro - Receitas: R$ 6.500, Gastos: R$ 2.300, Economia: R$ 500
INSERT INTO finances (transaction_type, category_id, description, amount, transaction_date, user_id, is_saving, created_at, updated_at) VALUES
('income', 1, 'Salário Janeiro', '5000.00', '2025-01-15', 5, false, NOW(), NOW()),
('income', 2, 'Freelance Janeiro', '1500.00', '2025-01-20', 5, false, NOW(), NOW()),
('expense', 4, 'Supermercado', '800.00', '2025-01-05', 5, false, NOW(), NOW()),
('expense', 5, 'Combustível', '300.00', '2025-01-10', 5, false, NOW(), NOW()),
('expense', 6, 'Aluguel', '1200.00', '2025-01-01', 5, false, NOW(), NOW()),
('expense', 4, 'Cofrinho Janeiro', '500.00', '2025-01-31', 5, true, NOW(), NOW());

-- Verificar se os dados foram inseridos
SELECT 'Categorias inseridas:' as info;
SELECT COUNT(*) as total_categories FROM finances_categories WHERE user_id = 5;

SELECT 'Transações inseridas para 2025:' as info;
SELECT COUNT(*) as total_transactions FROM finances WHERE user_id = 5 AND YEAR(transaction_date) = 2025;

SELECT 'Resumo por mês em 2025:' as info;
SELECT 
    MONTH(transaction_date) as mes,
    MONTHNAME(transaction_date) as nome_mes,
    SUM(CASE WHEN transaction_type = 'income' THEN amount ELSE 0 END) as receitas,
    SUM(CASE WHEN transaction_type = 'expense' AND is_saving = false THEN amount ELSE 0 END) as gastos,
    SUM(CASE WHEN is_saving = true THEN amount ELSE 0 END) as economia
FROM finances 
WHERE user_id = 5 AND YEAR(transaction_date) = 2025
GROUP BY MONTH(transaction_date), MONTHNAME(transaction_date)
ORDER BY MONTH(transaction_date);
