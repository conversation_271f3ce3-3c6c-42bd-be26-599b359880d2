-- Script para verificar se os dados foram inseridos corretamente

-- Verificar se a tabela annual_goals existe
SHOW TABLES LIKE 'annual_goals';

-- Verificar estrutura da tabela annual_goals
DESCRIBE annual_goals;

-- Verificar dados na tabela annual_goals
SELECT * FROM annual_goals WHERE user_id = 5;

-- Verificar dados na tabela finances
SELECT 
    COUNT(*) as total_transactions,
    SUM(CASE WHEN transaction_type = 'income' THEN 1 ELSE 0 END) as income_count,
    SUM(CASE WHEN transaction_type = 'expense' AND is_saving = false THEN 1 ELSE 0 END) as expense_count,
    SUM(CASE WHEN is_saving = true THEN 1 ELSE 0 END) as savings_count
FROM finances 
WHERE user_id = 5 AND YEAR(transaction_date) = 2024;

-- Verificar categorias
SELECT COUNT(*) as total_categories FROM finances_categories WHERE user_id = 5;

-- Verificar se a tabela annual_goals precisa ser criada
CREATE TABLE IF NOT EXISTS annual_goals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    goal_amount DECIMAL(10,2) NOT NULL,
    year INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_year (user_id, year),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Inserir meta anual se não existir
INSERT INTO annual_goals (user_id, goal_amount, year, created_at, updated_at) VALUES
(5, '8000.00', 2024, NOW(), NOW())
ON DUPLICATE KEY UPDATE 
goal_amount = '8000.00',
updated_at = NOW();

-- Verificar novamente
SELECT * FROM annual_goals WHERE user_id = 5;

-- Resumo final
SELECT 
    'Dados verificados:' as info,
    (SELECT COUNT(*) FROM annual_goals WHERE user_id = 5) as metas_anuais,
    (SELECT COUNT(*) FROM finances WHERE user_id = 5 AND YEAR(transaction_date) = 2024) as transacoes_2024,
    (SELECT COUNT(*) FROM finances_categories WHERE user_id = 5) as categorias;
