import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { 
    SERVER_NAME, 
    SERVER_DESCRIPTION, 
    SERVER_VERSION, 
    API_URL, 
    HEADERS, 
    ERROR_PHONE_REQUIRED, 
    ERROR_API_KEY_MISSING, 
    ERROR_API_REQUEST_FAILED, 
    ERROR_INVALID_RESPONSE,
    ERROR_NETWORK_ERROR,
    ERROR_TIMEOUT,
    ERROR_SERVER_ERROR,
    ERROR_BAD_REQUEST,
    ERROR_FORBIDDEN,
    ERROR_INTERNAL_ERROR,
    ERROR_PARSE_JSON,
    ERROR_INVALID_ENDPOINT,
    ERROR_UNAUTHORIZED,
    ERROR_NOT_FOUND,
    ERROR_VALIDATION_FAILED
} from "./constant.js";

// Interfaces para resposta da API melhorada
interface SuccessResponse {
    success: true;
    message?: string;
    data?: any;
    timestamp: string;
}

interface ErrorResponse {
    success: false;
    message: string;
    error?: string;
    timestamp: string;
    statusCode?: number;
}

interface PaginatedResponse {
    data: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
}

interface FinancialSummary {
    totalAmount: number;
    totalIncome: number;
    totalExpenses: number;
    balance: number;
}

interface PaginatedFinancialResponse extends PaginatedResponse {
    summary: FinancialSummary;
}

type ApiResponse = SuccessResponse | ErrorResponse;

// Função para mapear códigos de status HTTP para mensagens de erro
function getErrorMessage(status: number, statusText: string): string {
    switch (status) {
        case 400:
            return `${ERROR_BAD_REQUEST}: ${statusText}`;
        case 401:
            return `${ERROR_UNAUTHORIZED}: ${statusText}`;
        case 403:
            return `${ERROR_FORBIDDEN}: ${statusText}`;
        case 404:
            return `${ERROR_NOT_FOUND}: ${statusText}`;
        case 422:
            return `${ERROR_VALIDATION_FAILED}: ${statusText}`;
        case 500:
            return `${ERROR_INTERNAL_ERROR}: ${statusText}`;
        case 502:
        case 503:
        case 504:
            return `${ERROR_SERVER_ERROR}: ${statusText}`;
        default:
            return `${ERROR_API_REQUEST_FAILED}: ${status} ${statusText}`;
    }
}

// Função melhorada para requisições à API com tratamento robusto de erros
async function fetchFromApi(endpoint: string, options: any = {}): Promise<any> {
    const url = `${API_URL}${endpoint}`;
    
    try {
        // Validar se o endpoint é válido
        if (!endpoint || endpoint.trim() === '') {
            throw new Error(ERROR_INVALID_ENDPOINT);
        }

        // Validar se a URL da API está configurada
        if (!API_URL || API_URL.trim() === '') {
            throw new Error('API_URL not configured');
        }

        console.log(`[MCP-AgentWPP] Making request to: ${url}`);
        console.log(`[MCP-AgentWPP] Options:`, JSON.stringify(options, null, 2));

        const fetch = await import('node-fetch');
        
        // Configurar timeout padrão
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos
        
        const requestOptions = {
            ...options,
            signal: controller.signal,
            headers: {
                ...HEADERS,
                ...options.headers
            }
        };

        const response = await fetch.default(url, requestOptions);
        clearTimeout(timeoutId);

        console.log(`[MCP-AgentWPP] Response status: ${response.status} ${response.statusText}`);

        // Verificar se a resposta é bem-sucedida
        if (!response.ok) {
            const errorMessage = getErrorMessage(response.status, response.statusText);
            
            // Tentar obter detalhes do erro do corpo da resposta
            try {
                const errorBody = await response.text();
                console.error(`[MCP-AgentWPP] Error response body:`, errorBody);
                
                // Tentar parsear como JSON para obter mais detalhes
                try {
                    const errorJson = JSON.parse(errorBody);
                    if (errorJson.message) {
                        throw new Error(`${errorMessage} - ${errorJson.message}`);
                    }
                } catch (parseError) {
                    // Se não conseguir parsear, usar o corpo da resposta como texto
                    if (errorBody) {
                        throw new Error(`${errorMessage} - ${errorBody}`);
                    }
                }
            } catch (bodyError) {
                console.error(`[MCP-AgentWPP] Error reading response body:`, bodyError);
            }
            
            throw new Error(errorMessage);
        }

        // Tentar parsear a resposta JSON
        let data;
        try {
            const responseText = await response.text();
            console.log(`[MCP-AgentWPP] Response body:`, responseText);
            
            if (responseText) {
                data = JSON.parse(responseText);
            } else {
                data = null;
            }
        } catch (parseError) {
            console.error(`[MCP-AgentWPP] Error parsing JSON response:`, parseError);
            throw new Error(`${ERROR_PARSE_JSON}: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
        }

        console.log(`[MCP-AgentWPP] Parsed response:`, JSON.stringify(data, null, 2));
        return data;

    } catch (error: any) {
        console.error(`[MCP-AgentWPP] Request failed:`, error);
        
        // Tratar diferentes tipos de erro
        if (error.name === 'AbortError') {
            throw new Error(ERROR_TIMEOUT);
        }
        
        if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
            throw new Error(`${ERROR_NETWORK_ERROR}: ${error.message}`);
        }
        
        // Se já é um erro tratado, repassar
        if (error.message.includes(ERROR_BAD_REQUEST) || 
            error.message.includes(ERROR_UNAUTHORIZED) ||
            error.message.includes(ERROR_FORBIDDEN) ||
            error.message.includes(ERROR_NOT_FOUND) ||
            error.message.includes(ERROR_VALIDATION_FAILED) ||
            error.message.includes(ERROR_INTERNAL_ERROR) ||
            error.message.includes(ERROR_SERVER_ERROR) ||
            error.message.includes(ERROR_PARSE_JSON) ||
            error.message.includes(ERROR_INVALID_ENDPOINT) ||
            error.message.includes(ERROR_TIMEOUT) ||
            error.message.includes(ERROR_NETWORK_ERROR)) {
            throw error;
        }
        
        // Erro genérico
        throw new Error(`${ERROR_API_REQUEST_FAILED}: ${error.message}`);
    }
}

// Função para formatar resposta de sucesso
function formatResponse(data: any) {
    return {
        content: [
            {
                type: "text" as const,
                text: JSON.stringify(data, null, 2)
            }
        ]
    };
}

// Função para formatar resposta paginada com informações extras
function formatPaginatedResponse(data: PaginatedResponse) {
    const summary = {
        pagination: {
            currentPage: data.page,
            totalPages: data.totalPages,
            totalItems: data.total,
            itemsPerPage: data.limit,
            hasNextPage: data.hasNext,
            hasPreviousPage: data.hasPrevious
        },
        items: data.data
    };
    
    return {
        content: [
            {
                type: "text" as const,
                text: JSON.stringify(summary, null, 2)
            }
        ]
    };
}

// Função para formatar resposta financeira com resumo
function formatFinancialResponse(data: PaginatedFinancialResponse) {
    const summary = {
        summary: {
            totalAmount: data.summary.totalAmount,
            totalIncome: data.summary.totalIncome,
            totalExpenses: data.summary.totalExpenses,
            balance: data.summary.balance
        },
        pagination: {
            currentPage: data.page,
            totalPages: data.totalPages,
            totalItems: data.total,
            itemsPerPage: data.limit,
            hasNextPage: data.hasNext,
            hasPreviousPage: data.hasPrevious
        },
        transactions: data.data
    };
    
    return {
        content: [
            {
                type: "text" as const,
                text: JSON.stringify(summary, null, 2)
            }
        ]
    };
}

// Função inteligente para formatar resposta baseada no tipo
function formatSmartResponse(data: any) {
    // Verificar se é resposta paginada financeira
    if (data && data.summary && data.data && Array.isArray(data.data) && data.total !== undefined) {
        return formatFinancialResponse(data as PaginatedFinancialResponse);
    }
    
    // Verificar se é resposta paginada regular
    if (data && data.data && Array.isArray(data.data) && data.total !== undefined) {
        return formatPaginatedResponse(data as PaginatedResponse);
    }
    
    // Resposta regular
    return formatResponse(data);
}

// Função para formatar resposta de erro
function formatErrorResponse(error: string | Error, context?: string) {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const fullMessage = context ? `[${context}] ${errorMessage}` : errorMessage;
    
    return {
        content: [
            {
                type: "text" as const,
                text: JSON.stringify({
                    error: true,
                    message: fullMessage,
                    timestamp: new Date().toISOString()
                }, null, 2)
            }
        ],
        isError: true
    };
}

// Função wrapper para executar operações com try-catch
async function executeWithErrorHandling(
    operation: () => Promise<any>,
    context: string,
    validateInput?: (input: any) => void
): Promise<any> {
    try {
        const result = await operation();
        return formatSmartResponse(result);
    } catch (error: any) {
        console.error(`[MCP-AgentWPP] Error in ${context}:`, error);
        return formatErrorResponse(error, context);
    }
}

export function createServer(): McpServer {
    const server = new McpServer({
        name: SERVER_NAME,
        description: SERVER_DESCRIPTION,
        version: SERVER_VERSION
    });

    /*
    if (!HEADERS['Authorization']) {
        throw new Error(ERROR_API_KEY_MISSING);
    }
    */
    
    // Integration Tools
    server.tool("check-whatsapp-integration",
        "Check if phone has active integration",
        { input: z.object({ phone: z.string().describe("User phone number") }) },
        async ({ input: { phone } }) => {
            return executeWithErrorHandling(
                async () => {
                    if (!phone) {
                        throw new Error(ERROR_PHONE_REQUIRED);
                    }
                    return await fetchFromApi(`/agentwpp/check-integration/${phone}`, { headers: HEADERS });
                },
                "check-whatsapp-integration"
            );
        }
    );

    server.tool("validate-whatsapp-integration",
        "Validate phone with activation code",
        { input: z.object({ phone: z.string().describe("User phone number"), activation_code: z.string().describe("Activation code") }) },
        async ({ input: { phone, activation_code } }) => {
            return executeWithErrorHandling(
                async () => {
                    if (!phone || !activation_code) {
                        throw new Error(ERROR_PHONE_REQUIRED);
                    }
                    return await fetchFromApi(`/agentwpp/validate`, {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify({ phone, activation_code })
                    });
                },
                "validate-whatsapp-integration"
            );
        }
   );

    // DASHBOARD TOOLS
    server.tool("get-dashboard",
        "Retrieve user dashboard information",
        { input: z.object({ phone: z.string().describe("User phone number") }) },
        async ({ input: { phone } }) => {
            return executeWithErrorHandling(
                async () => {
                    if (!phone) {
                        throw new Error(ERROR_PHONE_REQUIRED);
                    }
                    return await fetchFromApi(`/agentwpp/dashboard/${phone}`, { headers: HEADERS });
                },
                "get-dashboard"
            );
        }
    );
    
    // TASK TOOLS
    server.tool("create-task",
        "Create a new task",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                task_type: z.enum(['appointment', 'task']).describe("Type of task"),
                name: z.string().describe("Task name"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                description: z.string().optional().describe("Task description (optional)"),
                task_date: z.string().optional().describe("Task date in ISO format (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/tasks', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-task"
            );
        }
    );

    server.tool("get-tasks",
        "List all tasks for a user with pagination",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                page: z.number().optional().describe("Page number (default: 1)"),
                limit: z.number().optional().describe("Items per page (default: 50)")
            })
        },
        async ({ input: { phone, page, limit } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/tasks/${phone}`;
                    const params = new URLSearchParams();
                    if (page) params.append('page', page.toString());
                    if (limit) params.append('limit', limit.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-tasks"
            );
        }
    );

    server.tool("get-task",
        "Get a specific task by ID",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Task ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/tasks/${phone}/${id}`, { headers: HEADERS });
                },
                "get-task"
            );
        }
    );

    server.tool("update-task",
        "Update an existing task",
        {
            input: z.object({
                id: z.number().describe("Task ID"),
                phone: z.string().describe("User phone number"),
                task_type: z.enum(['appointment', 'task']).optional().describe("Type of task (optional)"),
                name: z.string().optional().describe("Task name (optional)"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                description: z.string().optional().describe("Task description (optional)"),
                task_date: z.string().optional().describe("Task date in ISO format (optional)")
            })
        },
        async ({ input: { id, ...updateData } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/tasks/${id}`, {
                        method: 'PATCH',
                        headers: HEADERS,
                        body: JSON.stringify(updateData)
                    });
                },
                "update-task"
            );
        }
    );

    server.tool("delete-task",
        "Delete a task",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Task ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/tasks/${phone}/${id}`, {
                        method: 'DELETE',
                        headers: HEADERS
                    });
                },
                "delete-task"
            );
        }
    );

    server.tool("complete-task",
        "Mark a task as completed",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Task ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/tasks/${phone}/${id}/complete`, {
                        method: 'PATCH',
                        headers: HEADERS
                    });
                },
                "complete-task"
            );
        }
    );

    server.tool("create-task-category",
        "Create a new task category",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Category name")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/tasks/categories', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-task-category"
            );
        }
    );

    server.tool("get-task-categories",
        "List all task categories for a user",
        {
            input: z.object({
                phone: z.string().describe("User phone number")
            })
        },
        async ({ input: { phone } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/tasks/${phone}/categories`, { headers: HEADERS });
                },
                "get-task-categories"
            );
        }
    );

    // FINANCE TOOLS
    server.tool("create-finance",
        "Create a new financial record",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                transaction_type: z.enum(['income', 'expense']).describe("Type of transaction"),
                amount: z.string().describe("Amount as decimal string"),
                transaction_date: z.string().describe("Transaction date in ISO format"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                is_saving: z.boolean().optional().describe("Whether this is a saving (optional)"),
                description: z.string().optional().describe("Transaction description (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/finances', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-finance"
            );
        }
    );

    server.tool("get-finances",
        "List all financial records for a user with pagination",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                page: z.number().optional().describe("Page number (default: 1)"),
                limit: z.number().optional().describe("Items per page (default: 50)")
            })
        },
        async ({ input: { phone, page, limit } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/finances/${phone}`;
                    const params = new URLSearchParams();
                    if (page) params.append('page', page.toString());
                    if (limit) params.append('limit', limit.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-finances"
            );
        }
    );

    server.tool("get-finance",
        "Get a specific financial record by ID",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Finance record ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/finances/${phone}/${id}`, { headers: HEADERS });
                },
                "get-finance"
            );
        }
    );

    server.tool("update-finance",
        "Update an existing financial record",
        {
            input: z.object({
                id: z.number().describe("Finance record ID"),
                phone: z.string().describe("User phone number"),
                transaction_type: z.enum(['income', 'expense']).optional().describe("Type of transaction (optional)"),
                amount: z.string().optional().describe("Amount as decimal string (optional)"),
                transaction_date: z.string().optional().describe("Transaction date in ISO format (optional)"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                is_saving: z.boolean().optional().describe("Whether this is a saving (optional)"),
                description: z.string().optional().describe("Transaction description (optional)")
            })
        },
        async ({ input: { id, ...updateData } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/finances/${id}`, {
                        method: 'PATCH',
                        headers: HEADERS,
                        body: JSON.stringify(updateData)
                    });
                },
                "update-finance"
            );
        }
    );

    server.tool("delete-finance",
        "Delete a financial record",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Finance record ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/finances/${phone}/${id}`, {
                        method: 'DELETE',
                        headers: HEADERS
                    });
                },
                "delete-finance"
            );
        }
    );

    server.tool("get-finance-summary",
        "Get financial summary for a user",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                startDate: z.string().optional().describe("Start date in ISO format (optional)"),
                endDate: z.string().optional().describe("End date in ISO format (optional)")
            })
        },
        async ({ input: { phone, startDate, endDate } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/finances/${phone}/summary`;
                    const params = new URLSearchParams();
                    if (startDate) params.append('startDate', startDate);
                    if (endDate) params.append('endDate', endDate);
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-finance-summary"
            );
        }
    );

    server.tool("create-finance-category",
        "Create a new financial category",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Category name"),
                transaction_type: z.enum(['income', 'expense']).describe("Type of transaction"),
                color: z.string().optional().describe("Category color (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/finances/categories', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-finance-category"
            );
        }
    );

    server.tool("get-finance-categories",
        "List all financial categories for a user",
        {
            input: z.object({
                phone: z.string().describe("User phone number")
            })
        },
        async ({ input: { phone } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/finances/${phone}/categories`, { headers: HEADERS });
                },
                "get-finance-categories"
            );
        }
    );

    // IDEA TOOLS
    server.tool("create-idea",
        "Create a new idea",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Idea name"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                description: z.string().optional().describe("Idea description (optional)"),
                content: z.string().optional().describe("Idea content (optional)"),
                is_favorite: z.boolean().optional().describe("Whether this is a favorite idea (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/ideas', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-idea"
            );
        }
    );

    server.tool("get-ideas",
        "List all ideas for a user with pagination",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                page: z.number().optional().describe("Page number (default: 1)"),
                limit: z.number().optional().describe("Items per page (default: 50)")
            })
        },
        async ({ input: { phone, page, limit } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/ideas/${phone}`;
                    const params = new URLSearchParams();
                    if (page) params.append('page', page.toString());
                    if (limit) params.append('limit', limit.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-ideas"
            );
        }
    );

    server.tool("get-idea",
        "Get a specific idea by ID",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Idea ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/ideas/${phone}/${id}`, { headers: HEADERS });
                },
                "get-idea"
            );
        }
    );

    server.tool("update-idea",
        "Update an existing idea",
        {
            input: z.object({
                id: z.number().describe("Idea ID"),
                phone: z.string().describe("User phone number"),
                name: z.string().optional().describe("Idea name (optional)"),
                category_id: z.number().optional().describe("Category ID (optional)"),
                description: z.string().optional().describe("Idea description (optional)"),
                content: z.string().optional().describe("Idea content (optional)"),
                is_favorite: z.boolean().optional().describe("Whether this is a favorite idea (optional)")
            })
        },
        async ({ input: { id, ...updateData } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/ideas/${id}`, {
                        method: 'PATCH',
                        headers: HEADERS,
                        body: JSON.stringify(updateData)
                    });
                },
                "update-idea"
            );
        }
    );

    server.tool("delete-idea",
        "Delete an idea",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Idea ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/ideas/${phone}/${id}`, {
                        method: 'DELETE',
                        headers: HEADERS
                    });
                },
                "delete-idea"
            );
        }
    );

    server.tool("toggle-idea-favorite",
        "Toggle the favorite status of an idea",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                id: z.number().describe("Idea ID")
            })
        },
        async ({ input: { phone, id } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/ideas/${phone}/${id}/favorite`, {
                        method: 'PATCH',
                        headers: HEADERS
                    });
                },
                "toggle-idea-favorite"
            );
        }
    );

    server.tool("create-idea-category",
        "Create a new idea category",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Category name")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/ideas/categories', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-idea-category"
            );
        }
    );

    server.tool("get-idea-categories",
        "List all idea categories for a user",
        {
            input: z.object({
                phone: z.string().describe("User phone number")
            })
        },
        async ({ input: { phone } }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi(`/agentwpp/ideas/${phone}/categories`, { headers: HEADERS });
                },
                "get-idea-categories"
            );
        }
    );

    // ===== NOVAS FERRAMENTAS ESPECÍFICAS PARA WHATSAPP =====
    
    // Transações recentes com total do período
    server.tool("get-recent-finances",
        "Get recent financial transactions with period total count",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                limit: z.number().optional().describe("Number of recent transactions to return (default: 10)"),
                days: z.number().optional().describe("Number of days to count total transactions (default: 1)")
            })
        },
        async ({ input: { phone, limit, days } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/finances/${phone}/recent`;
                    const params = new URLSearchParams();
                    if (limit) params.append('limit', limit.toString());
                    if (days) params.append('days', days.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-recent-finances"
            );
        }
    );

    // Tarefas recentes com total do período
    server.tool("get-recent-tasks",
        "Get recent tasks with period total count",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                limit: z.number().optional().describe("Number of recent tasks to return (default: 10)"),
                days: z.number().optional().describe("Number of days to count total tasks (default: 1)")
            })
        },
        async ({ input: { phone, limit, days } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/tasks/${phone}/recent`;
                    const params = new URLSearchParams();
                    if (limit) params.append('limit', limit.toString());
                    if (days) params.append('days', days.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-recent-tasks"
            );
        }
    );

    // Ideias recentes com total do período
    server.tool("get-recent-ideas",
        "Get recent ideas with period total count",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                limit: z.number().optional().describe("Number of recent ideas to return (default: 10)"),
                days: z.number().optional().describe("Number of days to count total ideas (default: 1)")
            })
        },
        async ({ input: { phone, limit, days } }) => {
            return executeWithErrorHandling(
                async () => {
                    let endpoint = `/agentwpp/ideas/${phone}/recent`;
                    const params = new URLSearchParams();
                    if (limit) params.append('limit', limit.toString());
                    if (days) params.append('days', days.toString());
                    if (params.toString()) endpoint += `?${params.toString()}`;
                    
                    return await fetchFromApi(endpoint, { headers: HEADERS });
                },
                "get-recent-ideas"
            );
        }
    );

    // Criar tarefa rápida (sem data)
    server.tool("create-quick-task",
        "Create a quick task without date (simple task)",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Task name"),
                description: z.string().optional().describe("Task description (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    return await fetchFromApi('/agentwpp/tasks/quick', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(input)
                    });
                },
                "create-quick-task"
            );
        }
    );

    // Criar compromisso (tarefa com data e hora)
    server.tool("create-appointment",
        "Create an appointment (task with date and time)",
        {
            input: z.object({
                phone: z.string().describe("User phone number"),
                name: z.string().describe("Appointment name"),
                task_date: z.string().describe("Appointment date and time in ISO format"),
                description: z.string().optional().describe("Appointment description (optional)"),
                category_id: z.number().optional().describe("Category ID (optional)")
            })
        },
        async ({ input }) => {
            return executeWithErrorHandling(
                async () => {
                    const appointmentData = {
                        ...input,
                        task_type: 'appointment'
                    };
                    return await fetchFromApi('/agentwpp/tasks', {
                        method: 'POST',
                        headers: HEADERS,
                        body: JSON.stringify(appointmentData)
                    });
                },
                "create-appointment"
            );
        }
    );

    return server;
}
