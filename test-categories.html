<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Categorias</title>
</head>
<body>
    <h1>Teste de Categorias Financeiras</h1>
    <div id="result"></div>
    
    <script>
        async function testCategories() {
            const resultDiv = document.getElementById('result');
            
            try {
                // Primeiro, vamos tentar fazer login
                const loginResponse = await fetch('http://localhost:3000/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'password123',
                        device_uuid: 'test-device-uuid'
                    })
                });
                
                if (!loginResponse.ok) {
                    resultDiv.innerHTML = `<p style="color: red;">Erro no login: ${loginResponse.status} ${loginResponse.statusText}</p>`;
                    return;
                }
                
                const authData = await loginResponse.json();
                const token = authData.access_token;
                
                resultDiv.innerHTML += `<p style="color: green;">Login realizado com sucesso!</p>`;
                
                // Agora vamos testar o endpoint de categorias
                const categoriesResponse = await fetch('http://localhost:3000/finances/categories', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json',
                    }
                });
                
                if (!categoriesResponse.ok) {
                    resultDiv.innerHTML += `<p style="color: red;">Erro nas categorias: ${categoriesResponse.status} ${categoriesResponse.statusText}</p>`;
                    const errorText = await categoriesResponse.text();
                    resultDiv.innerHTML += `<p>Resposta: ${errorText}</p>`;
                    return;
                }
                
                const categories = await categoriesResponse.json();
                resultDiv.innerHTML += `<p style="color: green;">Categorias carregadas com sucesso!</p>`;
                resultDiv.innerHTML += `<pre>${JSON.stringify(categories, null, 2)}</pre>`;
                
            } catch (error) {
                resultDiv.innerHTML += `<p style="color: red;">Erro: ${error.message}</p>`;
                console.error('Erro detalhado:', error);
            }
        }
        
        // Executar o teste quando a página carregar
        window.onload = testCategories;
    </script>
</body>
</html>
