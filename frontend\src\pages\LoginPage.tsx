import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Eye, EyeOff, UserIcon } from 'lucide-react';

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Pegar a URL de redirecionamento ou usar dashboard como padrão
  const from = location.state?.from?.pathname || '/dashboard';

  // Debug: monitorar mudanças de estado
  useEffect(() => {
    console.log('Estado error mudou:', error);
  }, [error]);

  useEffect(() => {
    console.log('Estado email mudou:', email);
  }, [email]);

  useEffect(() => {
    console.log('Estado isSubmitting mudou:', isSubmitting);
  }, [isSubmitting]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('=== INÍCIO DO HANDLESUBMIT ===');
    
    // Limpar erro anterior
    setError('');
    setIsSubmitting(true);

    // Validação básica
    if (!email.trim() || !password.trim()) {
      console.log('Erro de validação básica');
      const errorMsg = 'Por favor, preencha todos os campos';
      setError(errorMsg);
      setIsSubmitting(false);
      return;
    }

    if (!email.includes('@')) {
      console.log('Erro de validação de email');
      const errorMsg = 'Por favor, insira um email válido';
      setError(errorMsg);
      setIsSubmitting(false);
      return;
    }

    try {
      console.log('Tentando fazer login...');
      await login(email.trim(), password);
      console.log('Login bem-sucedido, navegando...');
      
      // Só navegar se chegou até aqui sem erro
      navigate(from, { replace: true });
    } catch (error: unknown) {
      console.log('=== ERRO CAPTURADO NO HANDLESUBMIT ===');
      console.error('Erro no login:', error);

      // Melhor tratamento de erros
      let errorMessage = 'Erro ao fazer login. Tente novamente.';

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = (error as any).message;
      }

      console.log('Definindo mensagem de erro:', errorMessage);
      
      // Garantir que o estado seja atualizado
      setTimeout(() => {
        setError(errorMessage);
        console.log('setError executado via setTimeout');
      }, 10);
      
      // Não resetar o email em caso de erro
      // setEmail(''); // REMOVIDO
    } finally {
      console.log('Finalizando handleSubmit');
      setIsSubmitting(false);
    }
  }, [email, password, login, navigate, from]);

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="mx-auto h-12 w-12 bg-[#B4EB00] rounded-full flex items-center justify-center mb-4">
              <UserIcon className="h-6 w-6 text-gray-900" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900">Dupli</h2>
            <p className="text-gray-600 mt-2">Entre na sua conta para continuar</p>
          </div>

          {/* Error Message - Melhorado */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-red-800 text-sm font-medium">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
                disabled={isSubmitting}
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                Senha
              </label>
              <div className="relative">
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors pr-12"
                  placeholder="Sua senha"
                  disabled={isSubmitting}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  disabled={isSubmitting}
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-black text-white py-3 px-4 rounded-lg hover:bg-opacity-90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Entrando...' : 'Entrar'}
            </button>
          </form>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-gray-600">
              Não tem uma conta?{' '}
              <Link
                to="/register"
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                Cadastre-se
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
