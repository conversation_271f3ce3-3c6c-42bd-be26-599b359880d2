# API AgentWPP - Integração com n8n via WhatsApp

## Visão Geral

Esta API fornece endpoints para que o agente do n8n possa interagir com o sistema Dupli via WhatsApp, permitindo operações CRUD em:
- **Tarefas** (tasks)
- **Finanças** (finances) 
- **Ideias** (ideas)
- **Verificação de integração** WhatsApp

## Autenticação

- **Método**: API Key via header
- **Header**: `X-API-Key: {API_KEY}`
- **Identificação**: Via número de telefone WhatsApp do usuário
- **Formato do telefone**: Internacional (5511999999999)

### Headers Obrigatórios

```http
Content-Type: application/json
X-API-Key: your-secret-api-key-here
```

## Base URL e Health Check

```
GET /agentwpp/health
```

**Descrição**: Verifica se a API está funcionando

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2025-07-01T07:00:00.000Z"
}
```

---

## 🔗 Verificação de Integração

### Verificar se usuário possui integração WhatsApp

```http
GET /agentwpp/check-integration?phone={PHONE_NUMBER}
```

**Parâmetros:**
- `phone` (query string): Número do WhatsApp (ex: 5511999999999)

**Response:**
```json
{
  "hasIntegration": true,
  "status": "active",
  "userId": 5
}
```

---

## 📋 Tarefas (Tasks)

### Listar Tarefas

```http
GET /agentwpp/tasks?phone={PHONE_NUMBER}&page=1&limit=10
```

**Response:**
```json
{
  "tasks": [
    {
      "id": 1,
      "task_type": "task",
      "category_id": 2,
      "category_name": "Trabalho",
      "name": "Reunião com cliente",
      "description": "Discutir projeto novo",
      "task_date": "2025-07-01T10:00:00.000Z",
      "completed_at": null,
      "created_at": "2025-06-30T20:00:00.000Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Criar Tarefa

```http
POST /agentwpp/tasks
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional
- `task_type` (string, obrigatório): Tipo de tarefa
- `category_id` (integer, obrigatório): ID da categoria
- `name` (string, obrigatório): Nome da tarefa
- `description` (string, opcional): Descrição da tarefa
- `task_date` (string, opcional): Data da tarefa no formato ISO 8601

```json
{
  "phone": "5511999999999",
  "task_type": "task",
  "category_id": 2,
  "name": "Nova tarefa",
  "description": "Descrição da tarefa",
  "task_date": "2025-07-01T15:30:00.000Z"
}
```

### Atualizar Tarefa

```http
PUT /agentwpp/tasks/{id}
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional
- `name` (string, opcional): Nome atualizado da tarefa
- `description` (string, opcional): Descrição atualizada da tarefa

```json
{
  "phone": "5511999999999",
  "name": "Tarefa atualizada",
  "description": "Nova descrição"
}
```

### Completar Tarefa

```http
PATCH /agentwpp/tasks/{id}/complete
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional

```json
{
  "phone": "5511999999999"
}
```

### Deletar Tarefa

```http
DELETE /agentwpp/tasks/{id}?phone={PHONE_NUMBER}
```

### Listar Categorias de Tarefas

```http
GET /agentwpp/tasks/categories?phone={PHONE_NUMBER}
```

### Criar Categoria de Tarefa

```http
POST /agentwpp/tasks/categories
```

**Body:**
```json
{
  "phone": "5511999999999",
  "name": "Nova Categoria"
}
```

---

## 💰 Finanças (Finances)

### Listar Transações

```http
GET /agentwpp/finances?phone={PHONE_NUMBER}&page=1&limit=10
```

**Response:**
```json
{
  "finances": [
    {
      "id": 1,
      "transaction_type": "expense",
      "category_id": 3,
      "category_name": "Alimentação",
      "is_saving": false,
      "description": "Almoço",
      "amount": "25.50",
      "transaction_date": "2025-06-30T12:00:00.000Z",
      "created_at": "2025-06-30T20:00:00.000Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Criar Transação

```http
POST /agentwpp/finances
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional
- `transaction_type` (string, obrigatório): Tipo de transação, ex: despesa ou receita
- `category_id` (integer, obrigatório): ID da categoria da transação
- `is_saving` (boolean, opcional): Se é uma poupança
- `description` (string, opcional): Descrição da transação
- `amount` (string, obrigatório): Valor da transação
- `transaction_date` (string, opcional): Data da transação no formato ISO 8601

```json
{
  "phone": "5511999999999",
  "transaction_type": "expense",
  "category_id": 3,
  "is_saving": false,
  "description": "Compra supermercado",
  "amount": "150.00",
  "transaction_date": "2025-06-30T18:00:00.000Z"
}
```

### Atualizar Transação

```http
PUT /agentwpp/finances/{id}
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional
- `amount` (string, opcional): Novo valor da transação
- `description` (string, opcional): Nova descrição da transação

```json
{
  "phone": "5511999999999",
  "amount": "175.00",
  "description": "Compra supermercado atualizada"
}
```

### Deletar Transação

```http
DELETE /agentwpp/finances/{id}?phone={PHONE_NUMBER}
```

### Resumo Financeiro

```http
GET /agentwpp/finances/summary?phone={PHONE_NUMBER}&startDate=2025-06-01&endDate=2025-06-30
```

**Response:**
```json
{
  "totalIncome": 3000.00,
  "totalExpenses": 2200.00,
  "totalSavings": 500.00,
  "balance": 800.00,
  "period": {
    "start": "2025-06-01T00:00:00.000Z",
    "end": "2025-06-30T23:59:59.000Z"
  }
}
```

### Listar Categorias Financeiras

```http
GET /agentwpp/finances/categories?phone={PHONE_NUMBER}
```

### Criar Categoria Financeira

```http
POST /agentwpp/finances/categories
```

**Body:**
```json
{
  "phone": "5511999999999",
  "name": "Nova Categoria",
  "transaction_type": "expense",
  "color": "#FF5733"
}
```

---

## 💡 Ideias (Ideas)

### Listar Ideias

```http
GET /agentwpp/ideas?phone={PHONE_NUMBER}&page=1&limit=10
```

**Response:**
```json
{
  "ideas": [
    {
      "id": 1,
      "category_id": 1,
      "category_name": "Negócios",
      "name": "App de delivery",
      "description": "Aplicativo para entrega rápida",
      "content": "Detalhes da ideia...",
      "is_favorite": false,
      "created_at": "2025-06-30T20:00:00.000Z"
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

### Criar Ideia

```http
POST /agentwpp/ideas
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional
- `category_id` (integer, obrigatório): ID da categoria
- `name` (string, obrigatório): Nome da ideia
- `description` (string, opcional): Descrição breve da ideia
- `content` (string, opcional): Detalhamento da ideia
- `is_favorite` (boolean, opcional): Marcar como favorita

```json
{
  "phone": "5511999999999",
  "category_id": 1,
  "name": "Nova ideia",
  "description": "Descrição breve",
  "content": "Conteúdo detalhado da ideia",
  "is_favorite": false
}
```

### Atualizar Ideia

```http
PUT /agentwpp/ideas/{id}
```

**Body:**
```json
{
  "phone": "5511999999999",
  "name": "Ideia atualizada",
  "content": "Novo conteúdo"
}
```

### Marcar/Desmarcar como Favorita

```http
PATCH /agentwpp/ideas/{id}/favorite
```

**Body:**
- `phone` (string, obrigatório): Número do telefone no formato internacional

```json
{
  "phone": "5511999999999"
}
```

### Deletar Ideia

```http
DELETE /agentwpp/ideas/{id}?phone={PHONE_NUMBER}
```

### Listar Categorias de Ideias

```http
GET /agentwpp/ideas/categories?phone={PHONE_NUMBER}
```

### Criar Categoria de Ideia

```http
POST /agentwpp/ideas/categories
```

**Body:**
```json
{
  "phone": "5511999999999",
  "name": "Nova Categoria"
}
```

---

## 📊 Endpoints de Resumo

### Dashboard Resumido

```http
GET /agentwpp/dashboard?phone={PHONE_NUMBER}
```

**Response:**
```json
{
  "user": {
    "name": "João Silva",
    "timezone": "America/Sao_Paulo"
  },
  "tasks": {
    "completed": 5,
    "total": 10
  },
  "finances": {
    "spent": 2200.00,
    "budget": 2500.00,
    "income": 3000.00,
    "savings": 500.00
  },
  "ideas": {
    "today": 2,
    "total": 15,
    "favorites": 3
  }
}
```

---

## ⚠️ Códigos de Erro

| Código | Descrição |
|--------|-----------|
| 400 | Dados inválidos |
| 401 | API Key inválida |
| 403 | Usuário sem integração WhatsApp |
| 404 | Recurso não encontrado |
| 500 | Erro interno do servidor |

**Exemplo de resposta de erro:**
```json
{
  "error": "Unauthorized",
  "message": "API Key inválida",
  "statusCode": 401
}
```

---

## 🔧 Configuração

### Variáveis de Ambiente

```env
# API Key para n8n
N8N_API_KEY=your-secret-api-key-here

# Outras configurações existentes...
```

### Headers Obrigatórios

```http
Content-Type: application/json
X-API-Key: your-secret-api-key-here
```

---

## 📝 Notas Importantes

1. **Timezone**: Todas as datas são retornadas no timezone do usuário
2. **Paginação**: Limite máximo de 100 itens por página
3. **Rate Limiting**: 100 requests por minuto por API Key
4. **Validação**: Número de telefone deve estar no formato internacional (5511999999999)
5. **Integração**: Usuário deve ter integração WhatsApp ativa para usar a API

---

## 🚀 Exemplos de Uso no n8n

### Criar Tarefa via WhatsApp
```javascript
// Node HTTP Request no n8n
{
  "method": "POST",
  "url": "https://api.dupli.com/agentwpp/tasks",
  "headers": {
    "X-API-Key": "{{$node.env.N8N_API_KEY}}",
    "Content-Type": "application/json"
  },
  "body": {
    "phone": "{{$json.from}}",
    "task_type": "task",
    "name": "{{$json.body}}",
    "task_date": "{{$now.format('YYYY-MM-DD HH:mm:ss')}}"
  }
}
```

### Listar Tarefas Pendentes
```javascript
// Node HTTP Request no n8n
{
  "method": "GET",
  "url": "https://api.dupli.com/agentwpp/tasks?phone={{$json.from}}&limit=5",
  "headers": {
    "X-API-Key": "{{$node.env.N8N_API_KEY}}"
  }
}
```
