import React, { useState } from 'react';
import { X, Target, DollarSign } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSetAnnualGoal } from '../hooks/useAnnualGoal';
import { useToast } from '../contexts/ToastContext';

interface AnnualGoalModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const AnnualGoalModal: React.FC<AnnualGoalModalProps> = ({ isOpen, onClose }) => {
  const [goalAmount, setGoalAmount] = useState('');
  const setAnnualGoalMutation = useSetAnnualGoal();
  const { success, error } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!goalAmount.trim()) {
      return;
    }

    // Converter o valor para número (remover formatação)
    const numericValue = parseFloat(goalAmount.replace(/[^\d,]/g, '').replace(',', '.'));
    
    if (isNaN(numericValue) || numericValue <= 0) {
      error('Por favor, insira um valor válido para a meta.');
      return;
    }

    setAnnualGoalMutation.mutate(
      { goalAmount: numericValue },
      {
        onSuccess: () => {
          success('Meta anual definida com sucesso!');
          onClose();
          setGoalAmount('');
        },
        onError: (err) => {
          console.error('Erro ao definir meta anual:', err);
          error('Erro ao definir meta anual. Tente novamente.');
        }
      }
    );
  };

  const formatCurrency = (value: string) => {
    // Remove tudo que não é número
    const numericValue = value.replace(/[^\d]/g, '');
    
    if (!numericValue) return '';
    
    // Converte para número e formata
    const number = parseInt(numericValue) / 100;
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(number);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const formatted = formatCurrency(value);
    setGoalAmount(formatted);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            className="bg-white rounded-2xl p-6 w-full max-w-md shadow-xl"
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-[#B4EB00] rounded-lg">
                  <Target size={20} className="text-gray-900" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Definir Meta Anual
                </h2>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <X size={20} className="text-gray-500" />
              </button>
            </div>

            {/* Content */}
            <div className="mb-6">
              <p className="text-gray-600 text-sm mb-4">
                Defina sua meta de gastos para este ano. Isso ajudará você a acompanhar seu orçamento e manter o controle financeiro.
              </p>
              
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="goalAmount" className="block text-sm font-medium text-gray-700 mb-2">
                    Valor da meta anual
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <DollarSign size={16} className="text-gray-400" />
                    </div>
                    <input
                      type="text"
                      id="goalAmount"
                      value={goalAmount}
                      onChange={handleInputChange}
                      placeholder="R$ 0,00"
                      className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={setAnnualGoalMutation.isPending}
                    />
                  </div>
                </div>

                {/* Buttons */}
                <div className="flex gap-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="flex-1 px-4 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                    disabled={setAnnualGoalMutation.isPending}
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    disabled={!goalAmount.trim() || setAnnualGoalMutation.isPending}
                    className="flex-1 px-4 py-3 bg-gray-900 text-white rounded-lg hover:bg-gray-800 transition-colors font-medium disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {setAnnualGoalMutation.isPending ? (
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      'Definir Meta'
                    )}
                  </button>
                </div>
              </form>
            </div>

            {/* Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-blue-800 text-xs">
                💡 <strong>Dica:</strong> Uma boa meta anual considera seus gastos essenciais e deixa espaço para imprevistos e lazer.
              </p>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default AnnualGoalModal;
