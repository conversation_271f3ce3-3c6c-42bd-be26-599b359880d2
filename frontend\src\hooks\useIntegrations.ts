import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { 
  WhatsAppIntegrationResponseDto, 
  CreateWhatsAppIntegrationDto, 
  UpdateWhatsAppIntegrationDto,
  ValidateWhatsAppIntegrationDto
} from '../types/api';

// Query Keys
const INTEGRATION_KEYS = {
  all: ['integrations'] as const,
  whatsapp: () => [...INTEGRATION_KEYS.all, 'whatsapp'] as const,
  whatsappUser: () => [...INTEGRATION_KEYS.whatsapp(), 'user'] as const,
  whatsappById: (id: number) => [...INTEGRATION_KEYS.whatsapp(), id] as const,
};

// API Functions
const integrationsApi = {
  getWhatsAppIntegration: async (): Promise<WhatsAppIntegrationResponseDto | null> => {
    try {
      const response = await authenticatedApi.get('integrations/whatsapp');
      return response.json();
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },

  createWhatsAppIntegration: async (data: CreateWhatsAppIntegrationDto): Promise<WhatsAppIntegrationResponseDto> => {
    const response = await authenticatedApi.post('integrations/whatsapp', { json: data });
    return response.json();
  },

  updateWhatsAppIntegration: async (
    id: number, 
    data: UpdateWhatsAppIntegrationDto
  ): Promise<WhatsAppIntegrationResponseDto> => {
    const response = await authenticatedApi.patch(`integrations/whatsapp/${id}`, { json: data });
    return response.json();
  },

  validateWhatsAppIntegration: async (id: number): Promise<WhatsAppIntegrationResponseDto> => {
    const response = await authenticatedApi.post(`integrations/whatsapp/${id}/validate`);
    return response.json();
  },

  deleteWhatsAppIntegration: async (id: number): Promise<void> => {
    await authenticatedApi.delete(`integrations/whatsapp/${id}`);
  },

  validateWithActivationCode: async (data: ValidateWhatsAppIntegrationDto): Promise<WhatsAppIntegrationResponseDto> => {
    const response = await authenticatedApi.post('integrations/whatsapp/validate-code', { json: data });
    return response.json();
  },

  getWhatsAppUrl: async (id: number): Promise<{ whatsapp_url: string; activation_code: string }> => {
    const response = await authenticatedApi.get(`integrations/whatsapp/${id}/whatsapp-url`);
    return response.json();
  },
};

// Hooks
export const useWhatsAppIntegration = () => {
  return useQuery({
    queryKey: INTEGRATION_KEYS.whatsappUser(),
    queryFn: integrationsApi.getWhatsAppIntegration,
  });
};

export const useCreateWhatsAppIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: integrationsApi.createWhatsAppIntegration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_KEYS.whatsappUser() });
    },
  });
};

export const useUpdateWhatsAppIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateWhatsAppIntegrationDto }) =>
      integrationsApi.updateWhatsAppIntegration(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_KEYS.whatsappUser() });
    },
  });
};

export const useValidateWhatsAppIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: integrationsApi.validateWhatsAppIntegration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_KEYS.whatsappUser() });
    },
  });
};

export const useDeleteWhatsAppIntegration = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: integrationsApi.deleteWhatsAppIntegration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_KEYS.whatsappUser() });
    },
  });
};

export const useValidateWithActivationCode = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: integrationsApi.validateWithActivationCode,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: INTEGRATION_KEYS.whatsappUser() });
    },
  });
};

export const useGetWhatsAppUrl = () => {
  return useMutation({
    mutationFn: integrationsApi.getWhatsAppUrl,
  });
};
