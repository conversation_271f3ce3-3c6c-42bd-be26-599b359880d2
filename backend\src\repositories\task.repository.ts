import { Injectable, Inject } from '@nestjs/common';
import { Kysely } from 'kysely';
import { Database } from '../database.types';
import { DATABASE_CONNECTION } from '../database/database.provider';
import { BaseRepository } from './base.repository';
import { ITaskRepository } from './interfaces/task.repository.interface';
import { CreateTaskDto } from '../tasks/dto/create-task.dto';
import { UpdateTaskDto } from '../tasks/dto/update-task.dto';
import { TaskResponseDto } from '../tasks/dto/task-response.dto';
import { TimezoneUtils } from '../common/utils/timezone.utils';
import { ErrorUtils } from '../common/utils/error.utils';

@Injectable()
export class TaskRepository extends BaseRepository<TaskResponseDto, CreateTaskDto, UpdateTaskDto> implements ITaskRepository {
  
  constructor(@Inject(DATABASE_CONNECTION) db: Kysely<Database>) {
    super(db, 'TaskRepository');
  }

  get tableName(): keyof Database {
    return 'tasks';
  }

  get entityName(): string {
    return 'tarefa';
  }

  mapToResponseDto(entity: any, userTimezone: string = 'UTC'): TaskResponseDto {
    return {
      id: entity.id,
      task_type: entity.task_type,
      category_id: entity.category_id || undefined,
      category_name: entity.category_name || undefined,
      name: entity.name,
      description: entity.description || undefined,
      task_date: entity.task_date ? TimezoneUtils.toUserTimezone(entity.task_date, userTimezone) : undefined,
      user_id: entity.user_id,
      completed_at: entity.completed_at ? TimezoneUtils.toUserTimezone(entity.completed_at, userTimezone) : undefined,
      created_at: TimezoneUtils.toUserTimezone(entity.created_at, userTimezone),
      updated_at: TimezoneUtils.toUserTimezone(entity.updated_at, userTimezone)
    };
  }

  prepareCreateData(dto: CreateTaskDto, userId: number, userTimezone: string = 'UTC'): any {
    return {
      ...dto,
      user_id: userId,
      task_date: TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone),
      created_at: new Date(),
      updated_at: new Date()
    };
  }

  prepareUpdateData(dto: UpdateTaskDto, userTimezone: string = 'UTC'): any {
    const updateData = {
      ...dto,
      task_date: dto.task_date ? 
        TimezoneUtils.prepareDateForDatabase(dto.task_date, userTimezone) : 
        undefined,
      updated_at: new Date()
    };

    // Remover campos undefined
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });

    return updateData;
  }

  async findAllWithCategory(
    userId: number,
    userTimezone: string,
    page = 1,
    limit = 50,
    startDate?: string,
    endDate?: string
  ) {
    try {
      const offset = (page - 1) * limit;

      let query = this.db
        .selectFrom('tasks')
        .leftJoin('tasks_categories', 'tasks.category_id', 'tasks_categories.id')
        .select([
          'tasks.id',
          'tasks.task_type',
          'tasks.category_id',
          'tasks_categories.name as category_name',
          'tasks.name',
          'tasks.description',
          'tasks.task_date',
          'tasks.user_id',
          'tasks.completed_at',
          'tasks.created_at',
          'tasks.updated_at'
        ])
        .where('tasks.user_id', '=', userId);

      // Adicionar filtros de data se fornecidos
      if (startDate) {
        const startDateTime = TimezoneUtils.prepareDateForDatabase(startDate, userTimezone);
        query = query.where('tasks.task_date', '>=', startDateTime);
      }

      if (endDate) {
        const endDateTime = TimezoneUtils.prepareDateForDatabase(endDate, userTimezone);
        query = query.where('tasks.task_date', '<=', endDateTime);
      }

      const tasks = await query
        .orderBy('tasks.task_date', 'asc')
        .orderBy('tasks.created_at', 'desc')
        .limit(limit)
        .offset(offset)
        .execute();

      // Query para contar total com os mesmos filtros
      let countQuery = this.db
        .selectFrom('tasks')
        .select(this.db.fn.count('id').as('count'))
        .where('user_id', '=', userId);

      if (startDate) {
        const startDateTime = TimezoneUtils.prepareDateForDatabase(startDate, userTimezone);
        countQuery = countQuery.where('task_date', '>=', startDateTime);
      }

      if (endDate) {
        const endDateTime = TimezoneUtils.prepareDateForDatabase(endDate, userTimezone);
        countQuery = countQuery.where('task_date', '<=', endDateTime);
      }

      const total = await countQuery.executeTakeFirst();

      const data = tasks.map(task => this.mapToResponseDto(task, userTimezone));

      return {
        data,
        total: Number(total?.count || 0),
        page,
        limit
      };
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'listar tarefas com categoria', userId);
    }
  }

  async complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    try {
      // Verificar se a tarefa existe e pertence ao usuário
      const task = await this.findOne(id, userId, userTimezone);

      // Se a tarefa já está concluída, desmarcar. Se não, marcar como concluída
      const isCompleted = !!task.completed_at;
      
      await this.db
        .updateTable('tasks')
        .set({
          completed_at: isCompleted ? null : new Date(),
          updated_at: new Date()
        })
        .where('id', '=', id)
        .where('user_id', '=', userId)
        .execute();

      this.logger.log(`Tarefa ${id} ${isCompleted ? 'desmarcada como incompleta' : 'marcada como completa'} para usuário ${userId}`);
      return this.findOne(id, userId, userTimezone);
    } catch (error) {
      ErrorUtils.handleServiceError(this.logger, error, 'alterar status da tarefa', userId);
    }
  }
}