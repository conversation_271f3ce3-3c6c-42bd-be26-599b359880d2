import { Injectable, CanActivate, ExecutionContext, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class DevAwareAuthGuard extends AuthGuard('jwt') implements CanActivate {
  private readonly logger = new Logger(DevAwareAuthGuard.name);

  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest();

    // Verificar se o endpoint é público
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      this.logger.debug(`Endpoint público: ${request.method} ${request.url}`);
      return true;
    }

    this.logger.debug(`Verificando autenticação para: ${request.method} ${request.url}`);
    this.logger.debug(`NODE_ENV: ${process.env.NODE_ENV}`);
    this.logger.debug(`DEV_MODE_ENABLED: ${process.env.DEV_MODE_ENABLED}`);

    // Em modo de desenvolvimento, permitir bypass
    if (process.env.NODE_ENV === 'development' && process.env.DEV_MODE_ENABLED === 'true') {
      const devMode = request.headers['x-dev-mode'];
      const devUserId = request.headers['x-dev-user-id'];

      this.logger.debug(`Headers de dev: x-dev-mode=${devMode}, x-dev-user-id=${devUserId}`);

      if (devMode === 'true' && devUserId) {
        // Simular usuário autenticado
        request.user = {
          userId: parseInt(devUserId),
          email: '<EMAIL>'
        };
        this.logger.debug('Bypass de autenticação aplicado para desenvolvimento');
        return true;
      }
    }

    this.logger.debug('Usando autenticação JWT normal');
    // Usar autenticação JWT normal
    return super.canActivate(context);
  }
}
