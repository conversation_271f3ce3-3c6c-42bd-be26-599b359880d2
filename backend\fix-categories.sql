-- <PERSON>ript para corrigir o problema das categorias

-- 1. Verificar categorias existentes
SELECT 'CATEGORIAS EXISTENTES:' as info;
SELECT id, name, transaction_type, user_id FROM finances_categories WHERE user_id = 5;

-- 2. Verificar transações com category_id inválido
SELECT 'TRANSAÇÕES COM CATEGORY_ID INVÁLIDO:' as info;
SELECT f.id, f.category_id, f.description, f.amount
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND YEAR(f.transaction_date) = 2025
  AND f.category_id IS NOT NULL 
  AND fc.id IS NULL;

-- 3. Inserir categorias padrão se não existirem
INSERT IGNORE INTO finances_categories (id, name, transaction_type, color, user_id, created_at, updated_at) VALUES
(1, 'Salário', 'income', '#4CAF50', 5, NOW(), NOW()),
(2, 'Freelance', 'income', '#8BC34A', 5, NOW(), NOW()),
(3, 'Investimentos', 'income', '#CDDC39', 5, NOW(), NOW()),
(4, 'Alimentação', 'expense', '#F44336', 5, NOW(), NOW()),
(5, 'Transporte', 'expense', '#FF9800', 5, NOW(), NOW()),
(6, 'Moradia', 'expense', '#9C27B0', 5, NOW(), NOW()),
(7, 'Saúde', 'expense', '#E91E63', 5, NOW(), NOW()),
(8, 'Lazer', 'expense', '#3F51B5', 5, NOW(), NOW()),
(9, 'Educação', 'expense', '#607D8B', 5, NOW(), NOW()),
(10, 'Outros', 'expense', '#795548', 5, NOW(), NOW());

-- 4. Atualizar transações que têm category_id inválido para uma categoria padrão
UPDATE finances 
SET category_id = 4 -- Alimentação
WHERE user_id = 5 
  AND transaction_type = 'expense'
  AND category_id NOT IN (SELECT id FROM finances_categories WHERE user_id = 5)
  AND category_id IS NOT NULL;

-- 5. Atualizar transações sem categoria para uma categoria padrão
UPDATE finances 
SET category_id = 10 -- Outros
WHERE user_id = 5 
  AND transaction_type = 'expense'
  AND category_id IS NULL;

-- 6. Verificar resultado final
SELECT 'RESULTADO FINAL - GASTOS POR CATEGORIA:' as info;
SELECT 
    fc.name as category,
    SUM(f.amount) as amount,
    COUNT(f.id) as count
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND f.transaction_type = 'expense'
  AND YEAR(f.transaction_date) = 2025
GROUP BY f.category_id, fc.name
ORDER BY amount DESC;

-- 7. Verificar se ainda há transações sem categoria
SELECT 'TRANSAÇÕES AINDA SEM CATEGORIA:' as info;
SELECT COUNT(*) as count_sem_categoria
FROM finances f
LEFT JOIN finances_categories fc ON f.category_id = fc.id
WHERE f.user_id = 5 
  AND f.transaction_type = 'expense'
  AND YEAR(f.transaction_date) = 2025
  AND fc.id IS NULL;
