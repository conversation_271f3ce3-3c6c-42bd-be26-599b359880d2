import React, { useState } from 'react';
import { TrendingUp, TrendingDown, Wallet, ChevronRight, Plus, Edit2, <PERSON>gyBank, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import <PERSON><PERSON><PERSON> from '../components/PieChart';
import Header from '../components/Header';
import AnnualOverviewCard from '../components/AnnualOverviewCard';
import AddTransactionModal from '../components/AddTransactionModal';
import EditBudgetModal from '../components/EditBudgetModal';
import { useFinances, useFinancesSummary, useFinanceCategories, useFinanceExpensesByCategory } from '../hooks/useFinances';
import { useMonthlyComparison, useFinancialPercentages, useFormattedPercentages } from '../hooks/useMonthlyComparison';

const FinancesPage: React.FC = () => {
  const navigate = useNavigate();
  const [isAddTransactionModalOpen, setIsAddTransactionModalOpen] = useState(false);
  const [isEditBudgetModalOpen, setIsEditBudgetModalOpen] = useState(false);

  // Fetch data from API
  const { data: financesData, isLoading: financesLoading, error: financesError } = useFinances();
  const { data: summaryData, isLoading: summaryLoading } = useFinancesSummary();
  const { data: categoriesData } = useFinanceCategories();
  const { data: expensesByCategory } = useFinanceExpensesByCategory();
  const { data: comparisonData, isLoading: comparisonLoading } = useMonthlyComparison();
  
  // Calculate percentages with fallback
  const percentages = useFinancialPercentages(comparisonData);
  const formattedPercentages = useFormattedPercentages(percentages);

  const isLoading = financesLoading || summaryLoading || comparisonLoading;

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando dados financeiros...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (financesError) {
    return (
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="h-8 w-8 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Erro ao carregar dados</h2>
          <p className="text-gray-600 mb-4">
            Não foi possível carregar os dados financeiros. Tente novamente.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Tentar novamente
          </button>
        </div>
      </div>
    );
  }

  const handleBudgetUpdate = (newBudget: number) => {
    // TODO: Implement budget update API call
  };

  const handleAddTransaction = (transaction: {
    type: 'income' | 'expense' | 'savings';
    amount: number;
    category: string;
    description: string;
  }) => {
    // TODO: Implement add transaction API call
  };

  // Use real data from API or fallback to defaults
  const summary = summaryData || {
    totalIncome: 0,
    totalExpenses: 0,
    totalSavings: 0,
    balance: 0
  };

  // Calculate budget (for now, use a default or get from user config)
  const budget = 5000; // TODO: Get from user config

  // Reorganized quadrant data with real API data and calculated percentages
  const quadrantData = [
    {
      title: 'Cofrinho',
      value: summary.totalSavings,
      trend: { value: percentages.savingsChange, label: 'este mês' },
      icon: PiggyBank,
      route: '/finances/cofrinho'
    },
    {
      title: 'Receita',
      value: summary.totalIncome,
      trend: { value: percentages.incomeChange, label: 'vs mês anterior' },
      icon: TrendingUp,
      route: '/finances/income'
    },
    {
      title: 'Despesas',
      value: summary.totalExpenses,
      trend: { value: percentages.expensesChange, label: 'vs mês anterior' },
      icon: TrendingDown,
      route: '/finances/expenses'
    },
    {
      title: 'Disponível',
      value: summary.balance,
      trend: { value: percentages.balanceChange, label: 'este mês' },
      icon: Wallet,
      route: '/finances/overview'
    }
  ];
  

  return (
    <div className="relative min-h-screen bg-[#F7F7F7]">
      <Header 
        onAddClick={() => setIsAddTransactionModalOpen(true)}
        onEditBudgetClick={() => setIsEditBudgetModalOpen(true)}
      />

      <div className="max-w-4xl mx-auto pt-20 px-4 pb-24 md:pb-6 space-y-6">
        {/* Financial quadrant */}
        <div className="grid grid-cols-2 gap-4">
          {quadrantData.map((item, index) => {
            const Icon = item.icon;
            const isCofrinho = item.title === 'Cofrinho';
            return (
              <div
                key={item.title}
                onClick={() => item.route !== '/finances/overview' && navigate(item.route)}
                className={`
                  p-4 rounded-xl shadow-sm transition-all
                  ${isCofrinho 
                    ? 'bg-gradient-to-br from-[#B4EB00] to-[#9FD700] cursor-pointer hover:shadow-md' 
                    : 'bg-white'
                  }
                  ${item.route !== '/finances/overview' && !isCofrinho ? 'cursor-pointer hover:shadow-md' : ''}
                `}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg ${isCofrinho ? 'bg-white/20' : 'bg-gray-50'}`}>
                    <Icon size={18} className={isCofrinho ? 'text-gray-900' : 'text-gray-600'} />
                  </div>
                  <span className={`text-xs ${isCofrinho ? 'text-gray-900 font-medium' : 'text-gray-600'}`}>
                    {item.title}
                  </span>
                </div>
                <p className={`text-xl font-bold ${isCofrinho ? 'text-gray-900' : 'text-gray-900'}`}>
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(item.value)}
                </p>
                {item.trend && (
                  <p className={`text-xs mt-1 ${
                    isCofrinho 
                      ? 'text-gray-700'
                      : item.trend.value >= 0 ? 'text-[#4CAF50]' : 'text-[#FF3B30]'
                  }`}>
                    {item.trend.value > 0 ? '+' : ''}{item.trend.value}% {item.trend.label}
                  </p>
                )}
              </div>
            );
          })}
        </div>

        {/* Annual Overview */}
        <div>
          <AnnualOverviewCard />
        </div>

        {/* Categories section */}
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Distribuição de gastos por categoria
            </h2>
            <button
              onClick={() => navigate('/finances/categories')}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <Edit2 size={20} className="text-gray-600" />
            </button>
          </div>

          {categoriesData && categoriesData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Pie chart */}
              <div className="flex justify-center items-center">
                <PieChart
                  data={expensesByCategory?.map((item, index) => ({
                    name: item.category,
                    value: item.amount,
                    color: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'][index % 5]
                  })) || []}
                  size={240}
                />
              </div>

              {/* Categories list */}
              <div className="space-y-3">
                {expensesByCategory?.map((item, index) => {
                  const totalExpenses = expensesByCategory.reduce((sum, cat) => sum + cat.amount, 0);
                  const percentage = totalExpenses > 0 ? (item.amount / totalExpenses) * 100 : 0;
                  
                  return (
                    <motion.div
                      key={item.category}
                      whileHover={{ scale: 1.02 }}
                      className="flex items-center justify-between p-3 rounded-xl hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'][index % 5] }}
                        />
                        <span className="font-medium">{item.category}</span>
                        <span className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                          {item.count} {item.count === 1 ? 'transação' : 'transações'}
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL'
                          }).format(item.amount)}
                        </p>
                        <p className="text-sm text-gray-500">
                          {percentage.toFixed(1)}%
                        </p>
                      </div>
                    </motion.div>
                  );
                }) || []}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Nenhuma categoria encontrada</p>
              <button
                onClick={() => navigate('/finances/categories')}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar categorias
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal
        isOpen={isAddTransactionModalOpen}
        onClose={() => setIsAddTransactionModalOpen(false)}
        onAdd={handleAddTransaction}
      />

      {/* Edit Budget Modal */}
      <EditBudgetModal
        isOpen={isEditBudgetModalOpen}
        onClose={() => setIsEditBudgetModalOpen(false)}
        currentBudget={budget}
        onSave={handleBudgetUpdate}
      />
    </div>
  );
};

export default FinancesPage;