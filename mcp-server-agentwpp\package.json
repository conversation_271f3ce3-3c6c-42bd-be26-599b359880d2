{"name": "remote-mcp-server", "version": "1.0.0", "description": "The example of MCP server.", "main": "dist/index.js", "files": ["dist"], "keywords": ["mcp"], "author": "partychen", "license": "MIT", "scripts": {"build": "tsc && shx chmod +x dist/index.js", "watch": "tsc --watch", "start": "node ./dist/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "dotenv": "^16.5.0", "express": "^5.1.0", "zod": "^3.24.2", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "^22.13.10", "shx": "^0.3.4", "typescript": "^5.8.2"}}