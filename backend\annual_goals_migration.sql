-- Migração para criar tabela annual_goals
-- Execute este SQL no phpMyAdmin

CREATE TABLE IF NOT EXISTS `annual_goals` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` BIGINT UNSIGNED NOT NULL,
    `goal_amount` DECIMAL(10, 2) NOT NULL,
    `year` INT NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_year` (`year`),
    UNIQUE KEY `unique_user_year` (`user_id`, `year`),
    CONSTRAINT `fk_annual_goals_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);

-- Inserir alguns dados de exemplo (opcional)
-- INSERT INTO annual_goals (user_id, goal_amount, year) VALUES 
-- (1, 25000.00, 2024),
-- (1, 30000.00, 2025);



"id"	"name"	"email"	"password"	"phone"	"timezone"	"created_at"	"updated_at"	"deleted_at"
"5"	"Daivison Cardoso"	"<EMAIL>"	"$2b$10$bpcChLgBmMTghAByQC4uIe5ifk0mzgb0BplaZFGranVsLyf3dM47i"	\N	"America/Sao_Paulo"	"2025-06-20 17:43:55"	"2025-06-20 17:43:55"	\N
"6"	"Bernardo mota"	"<EMAIL>"	"$2b$10$i17w7wT2YFwVWF4cmg71COL6V8.zhgVrwB39AQQ5qKEBmddIDqoRa"	"41999794128"	"America/Sao_Paulo"	"2025-06-20 22:14:04"	"2025-06-20 22:17:13"	\N
