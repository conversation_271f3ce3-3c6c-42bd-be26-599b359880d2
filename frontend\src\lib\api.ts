import ky from 'ky';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// Token refresh mutex to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

// Função para obter o token do localStorage
const getToken = (): string | null => {
  return localStorage.getItem('access_token');
};

// Função para obter o device UUID
const getDeviceUuid = (): string => {
  let deviceUuid = localStorage.getItem('device_uuid');
  if (!deviceUuid) {
    deviceUuid = crypto.randomUUID();
    localStorage.setItem('device_uuid', deviceUuid);
  }
  return deviceUuid;
};

// Simple token expiration check (with 5 minute buffer)
const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const bufferTime = 5 * 60; // 5 minutes buffer
    return payload.exp < (currentTime + bufferTime);
  } catch (error) {
    console.error('Error parsing JWT token:', error);
    // Se o token está corrompido, limpar do localStorage
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
    return true;
  }
};

// Cliente HTTP base
export const api = ky.create({
  prefixUrl: API_URL,
  timeout: 30000,
  credentials: 'include',
  retry: {
    limit: 2,
    methods: ['get', 'put', 'head', 'delete', 'options', 'trace'],
    statusCodes: [408, 413, 429, 500, 502, 503, 504],
  },
  hooks: {
    beforeRequest: [
      (request) => {
        // Adicionar device UUID em todas as requisições
        const deviceUuid = getDeviceUuid();
        request.headers.set('X-Device-UUID', deviceUuid);

        // Em desenvolvimento, sempre adicionar headers de dev
        if (import.meta.env.DEV && import.meta.env.VITE_DEV_MODE === 'true') {
          request.headers.set('X-Dev-Mode', 'true');
          request.headers.set('X-Dev-User-Id', '5');
        }

        // Adicionar token JWT se disponível (exceto para rotas de auth)
        const token = getToken();
        const url = request.url;
        const isAuthRoute = url.includes('/auth/login') || url.includes('/auth/register') || url.includes('/auth/refresh');

        if (token && !isAuthRoute) {
          // Check if token is expired before making the request
          if (isTokenExpired(token)) {
            // Don't add the expired token, let the afterResponse hook handle the refresh
          } else {
            request.headers.set('Authorization', `Bearer ${token}`);
          }
        }
      },
    ],
    afterResponse: [
      async (request, options, response) => {
        // Se receber 401, tentar refresh token primeiro
        if (response.status === 401) {
          const refreshToken = localStorage.getItem('refresh_token');
          const currentPath = window.location.pathname;

          // Não tentar refresh se já estamos em rotas de auth
          if (refreshToken && !currentPath.includes('/login') && !currentPath.includes('/register')) {
            try {
              // Use mutex to prevent multiple simultaneous refresh attempts
              if (isRefreshing) {
                // Wait for the ongoing refresh to complete
                await refreshPromise;

                // Try the original request again with the new token
                const newToken = getToken();
                if (newToken) {
                  const newRequest = request.clone();
                  newRequest.headers.set('Authorization', `Bearer ${newToken}`);
                  return ky(newRequest);
                }
                throw new Error('No token available after refresh');
              }

              isRefreshing = true;

              // Create refresh promise
              refreshPromise = api.post('auth/refresh', {
                json: {
                  refresh_token: refreshToken,
                  device_uuid: getDeviceUuid()
                },
                retry: {
                  limit: 2,
                  methods: ['post'],
                  statusCodes: [408, 413, 429, 500, 502, 503, 504],
                }
              }).json();

              const newAuthData = await refreshPromise;

              // Handle nested response structure: response.data.data or response.data or response
              const authData = newAuthData.data?.data || newAuthData.data || newAuthData;
              
              // Check if response has the expected structure
              const accessToken = authData.access_token;
              const refreshTokenNew = authData.refresh_token;
              const userData = authData.user;

              if (!accessToken || !refreshTokenNew) {
                throw new Error('Invalid refresh response structure');
              }

              // Atualizar tokens
              localStorage.setItem('access_token', accessToken);
              localStorage.setItem('refresh_token', refreshTokenNew);
              if (userData) {
                localStorage.setItem('user_data', JSON.stringify(userData));
              }

              // Reset refresh state
              isRefreshing = false;
              refreshPromise = null;

              // Recriar a requisição original com o novo token
              const newRequest = request.clone();
              newRequest.headers.set('Authorization', `Bearer ${accessToken}`);

              // Fazer nova tentativa da requisição original
              return ky(newRequest);
            } catch (refreshError) {
              // Reset refresh state
              isRefreshing = false;
              refreshPromise = null;

              // Only logout if it's an authentication error, not a network error
              const isNetworkError = refreshError.name === 'TypeError' ||
                                   refreshError.message?.includes('fetch') ||
                                   refreshError.message?.includes('network') ||
                                   refreshError.message?.includes('Failed to fetch');

              if (!isNetworkError) {
                // Authentication error - clear tokens and redirect
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                localStorage.removeItem('user_data');

                if (!currentPath.includes('/login')) {
                  window.location.href = '/login';
                }
              } else {
                // For network errors, just return the original response
                // The user will see the error but won't be logged out
              }
            }
          } else {
            // Sem refresh token ou já em rota de auth, fazer logout
            localStorage.removeItem('access_token');
            localStorage.removeItem('refresh_token');
            localStorage.removeItem('user_data');

            if (!currentPath.includes('/login')) {
              window.location.href = '/login';
            }
          }
        }

        return response;
      },
    ],
  },
});

// Cliente para requisições autenticadas
export const authenticatedApi = api.extend({
  hooks: {
    beforeRequest: [
      (request) => {
        const token = getToken();

        // Em desenvolvimento, permitir bypass da autenticação
        if (import.meta.env.DEV && import.meta.env.VITE_DEV_MODE === 'true') {
          if (!token) {
            // Usar headers de desenvolvimento quando não há token
            request.headers.set('X-Dev-Mode', 'true');
            request.headers.set('X-Dev-User-Id', '5');
            return;
          }
        }

        if (!token) {
          throw new Error('Token de autenticação não encontrado');
        }
      },
    ],
  },
});

// Tipos para autenticação
export interface LoginRequest {
  email: string;
  password: string;
  device_uuid: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  phone?: string;
  timezone?: string;
  device_uuid: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: number;
    name: string;
    email: string;
    phone?: string;
    timezone: string;
  };
}

// Funções de autenticação
export const authApi = {
  login: async (credentials: Omit<LoginRequest, 'device_uuid'>): Promise<AuthResponse> => {
    const deviceUuid = getDeviceUuid();
    const response = await api.post('auth/login', {
      json: {
        ...credentials,
        device_uuid: deviceUuid,
      },
    });
    return response.json();
  },

  register: async (userData: Omit<RegisterRequest, 'device_uuid'>): Promise<AuthResponse> => {
    const deviceUuid = getDeviceUuid();
    const response = await api.post('auth/register', {
      json: {
        ...userData,
        device_uuid: deviceUuid,
      },
    });
    return response.json();
  },

  logout: async (): Promise<void> => {
    try {
      await authenticatedApi.post('auth/logout');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      // Limpar dados locais independentemente do resultado
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_data');
    }
  },

  refreshToken: async (): Promise<AuthResponse> => {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      throw new Error('Refresh token não encontrado');
    }

    const response = await api.post('auth/refresh', {
      json: { refresh_token: refreshToken },
    });
    return response.json();
  },
};

// Utilitários para gerenciar tokens
export const tokenUtils = {
  setTokens: (authResponse: AuthResponse) => {
    localStorage.setItem('access_token', authResponse.access_token);
    localStorage.setItem('refresh_token', authResponse.refresh_token);
    localStorage.setItem('user_data', JSON.stringify(authResponse.user));
  },

  clearTokens: () => {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_data');
  },

  getUser: () => {
    try {
      const userData = localStorage.getItem('user_data');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data from localStorage:', error);
      // Clear corrupted data
      localStorage.removeItem('user_data');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      return null;
    }
  },

  isAuthenticated: (): boolean => {
    return !!getToken();
  },

  // Check if JWT token is expired (with 5 minute buffer)
  isTokenExpired: (token?: string): boolean => {
    const tokenToCheck = token || getToken();
    if (!tokenToCheck) return true;

    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const bufferTime = 5 * 60; // 5 minutes buffer
      return payload.exp < (currentTime + bufferTime);
    } catch (error) {
      console.error('Error parsing JWT token:', error);
      return true;
    }
  },

  // Check if token needs refresh (within 10 minutes of expiry)
  needsRefresh: (token?: string): boolean => {
    const tokenToCheck = token || getToken();
    if (!tokenToCheck) return false;

    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const refreshBuffer = 10 * 60; // 10 minutes buffer
      return payload.exp < (currentTime + refreshBuffer);
    } catch (error) {
      console.error('Error parsing JWT token:', error);
      return false;
    }
  },
};
