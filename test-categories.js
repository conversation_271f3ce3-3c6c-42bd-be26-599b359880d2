// Script para testar o problema das categorias
// Usando fetch nativo do Node.js (v18+)

const API_URL = 'http://localhost:3000';
const DEVICE_UUID = 'test-device-uuid';

const headers = {
  'Content-Type': 'application/json',
  'x-device-uuid': DEVICE_UUID
};

async function testCategories() {
  try {
    console.log('🔍 Testando problema das categorias...\n');

    // Teste 1: Buscar gastos por categoria para 2025
    console.log('📊 Teste 1: Buscando gastos por categoria para 2025...');
    const categoryResponse = await fetch(`${API_URL}/finances/expenses-by-category?startDate=2025-01-01&endDate=2025-12-31`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${categoryResponse.status} ${categoryResponse.statusText}`);
    
    if (categoryResponse.ok) {
      const categoryData = await categoryResponse.json();
      console.log('✅ Gastos por categoria obtidos com sucesso:');
      console.log('📊 Total de categorias:', categoryData.length);
      categoryData.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.category}: R$ ${cat.amount} (${cat.count} transações)`);
      });
    } else {
      const errorData = await categoryResponse.text();
      console.log('❌ Erro ao buscar gastos por categoria:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 2: Buscar todas as categorias
    console.log('🏷️ Teste 2: Buscando todas as categorias...');
    const allCategoriesResponse = await fetch(`${API_URL}/finances/categories`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${allCategoriesResponse.status} ${allCategoriesResponse.statusText}`);
    
    if (allCategoriesResponse.ok) {
      const allCategoriesData = await allCategoriesResponse.json();
      console.log('✅ Categorias obtidas com sucesso:');
      console.log('📊 Total de categorias:', allCategoriesData.length);
      allCategoriesData.forEach((cat, index) => {
        console.log(`${index + 1}. ID: ${cat.id}, Nome: "${cat.name}", Tipo: ${cat.transaction_type}, User: ${cat.user_id}`);
      });
    } else {
      const errorData = await allCategoriesResponse.text();
      console.log('❌ Erro ao buscar categorias:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 3: Buscar transações para verificar category_id
    console.log('💳 Teste 3: Buscando transações para verificar category_id...');
    const transactionsResponse = await fetch(`${API_URL}/finances?limit=10`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${transactionsResponse.status} ${transactionsResponse.statusText}`);
    
    if (transactionsResponse.ok) {
      const transactionsData = await transactionsResponse.json();
      console.log('✅ Transações obtidas com sucesso:');
      console.log('📊 Total de transações na página:', transactionsData.finances?.length || 0);
      
      if (transactionsData.finances && transactionsData.finances.length > 0) {
        console.log('🔍 Primeiras 5 transações:');
        transactionsData.finances.slice(0, 5).forEach((transaction, index) => {
          console.log(`${index + 1}. ID: ${transaction.id}, Tipo: ${transaction.transaction_type}, Category ID: ${transaction.category_id}, Category Name: "${transaction.category_name}", Amount: ${transaction.amount}`);
        });
      }
    } else {
      const errorData = await transactionsResponse.text();
      console.log('❌ Erro ao buscar transações:');
      console.log(errorData);
    }

  } catch (error) {
    console.error('❌ Erro durante os testes:', error.message);
  }
}

testCategories();
