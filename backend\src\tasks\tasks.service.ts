import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto, TaskListResponseDto } from './dto/task-response.dto';
import { CreateTaskCategoryDto } from './dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from './dto/update-task-category.dto';
import { TaskCategoryResponseDto } from './dto/task-category-response.dto';
import { TaskRepository } from '../repositories/task.repository';
import { TaskCategoryRepository } from '../repositories/task-category.repository';

@Injectable()
export class TasksService {
  private readonly logger = new Logger(TasksService.name);

  constructor(
    private readonly taskRepository: TaskRepository,
    private readonly taskCategoryRepository: TaskCategoryRepository,
  ) {}

  // Métodos para tarefas
  async create(createTaskDto: CreateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    return this.taskRepository.create(createTaskDto, userId, userTimezone);
  }

  async findAll(
    userId: number,
    userTimezone: string,
    page = 1,
    limit = 50,
    startDate?: string,
    endDate?: string
  ): Promise<TaskListResponseDto> {
    const result = await this.taskRepository.findAllWithCategory(
      userId,
      userTimezone,
      page,
      limit,
      startDate,
      endDate
    );
    return {
      tasks: result.data,
      total: result.total,
      page: result.page,
      limit: result.limit
    };
  }

  async findOne(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    return this.taskRepository.findOne(id, userId, userTimezone);
  }

  async update(id: number, updateTaskDto: UpdateTaskDto, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    return this.taskRepository.update(id, updateTaskDto, userId, userTimezone);
  }

  async remove(id: number, userId: number): Promise<void> {
    return this.taskRepository.remove(id, userId);
  }

  async complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto> {
    return this.taskRepository.complete(id, userId, userTimezone);
  }

  // Métodos para categorias de tarefas
  async createCategory(createCategoryDto: CreateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto> {
    return this.taskCategoryRepository.create(createCategoryDto, userId);
  }

  async findAllCategories(userId: number): Promise<TaskCategoryResponseDto[]> {
    return this.taskCategoryRepository.findAllOrderedByName(userId);
  }

  async findOneCategory(id: number, userId: number): Promise<TaskCategoryResponseDto> {
    return this.taskCategoryRepository.findOne(id, userId);
  }

  async updateCategory(id: number, updateCategoryDto: UpdateTaskCategoryDto, userId: number): Promise<TaskCategoryResponseDto> {
    return this.taskCategoryRepository.update(id, updateCategoryDto, userId);
  }

  async removeCategory(id: number, userId: number): Promise<void> {
    // Verificar se a categoria está sendo usada
    const isInUse = await this.taskCategoryRepository.checkCategoryInUse(id, userId);
    
    if (isInUse) {
      throw new HttpException(
        'Não é possível remover categoria que está sendo usada por tarefas',
        HttpStatus.BAD_REQUEST
      );
    }

    return this.taskCategoryRepository.remove(id, userId);
  }
}