# Melhorias para Ferramentas WhatsApp no MCP Server

## Visão Geral

Este documento descreve as melhorias implementadas no servidor MCP para agentwpp, focando nas necessidades específicas de conversas WhatsApp integradas com N8N.

## Novas Funcionalidades

### 1. Ferramentas de Consulta Recente

#### `get-recent-finances`
- **Propósito**: Obter transações financeiras recentes com total do período
- **Parâmetros**:
  - `phone` (obrigatório): Número de telefone do usuário
  - `limit` (opcional): Número de transações recentes a retornar (padrão: 10)
  - `days` (opcional): Número de dias para calcular total (padrão: 1)
- **Retorno**: Lista de transações recentes + total de transações no período
- **Exemplo de uso**: "Me mostre as últimas 5 transações financeiras de hoje"

#### `get-recent-tasks`
- **Propósito**: Obter tarefas recentes com total do período
- **Parâmetros**:
  - `phone` (obrigatório): Número de telefone do usuário
  - `limit` (opcional): Número de tarefas recentes a retornar (padrão: 10)
  - `days` (opcional): Número de dias para calcular total (padrão: 1)
- **Retorno**: Lista de tarefas recentes + total de tarefas no período
- **Exemplo de uso**: "Quais são as minhas últimas 3 tarefas criadas hoje?"

#### `get-recent-ideas`
- **Propósito**: Obter ideias recentes com total do período
- **Parâmetros**:
  - `phone` (obrigatório): Número de telefone do usuário
  - `limit` (opcional): Número de ideias recentes a retornar (padrão: 10)
  - `days` (opcional): Número de dias para calcular total (padrão: 1)
- **Retorno**: Lista de ideias recentes + total de ideias no período
- **Exemplo de uso**: "Me mostre as últimas 5 ideias que criei esta semana"

### 2. Ferramentas de Criação Rápida

#### `create-quick-task`
- **Propósito**: Criar tarefas simples sem data (tasks rápidas)
- **Parâmetros**:
  - `phone` (obrigatório): Número de telefone do usuário
  - `name` (obrigatório): Nome da tarefa
  - `description` (opcional): Descrição da tarefa
- **Retorno**: Tarefa criada
- **Exemplo de uso**: "Criar tarefa: Comprar leite"

#### `create-appointment`
- **Propósito**: Criar compromissos com data e hora específicas
- **Parâmetros**:
  - `phone` (obrigatório): Número de telefone do usuário
  - `name` (obrigatório): Nome do compromisso
  - `task_date` (obrigatório): Data e hora em formato ISO
  - `description` (opcional): Descrição do compromisso
  - `category_id` (opcional): ID da categoria
- **Retorno**: Compromisso criado
- **Exemplo de uso**: "Criar compromisso: Reunião com cliente amanhã às 14h"

## Melhorias na API Backend

### Novos Endpoints

#### `GET /agentwpp/finances/{phone}/recent`
- Retorna transações recentes com contagem total do período
- Query params: `limit`, `days`

#### `GET /agentwpp/tasks/{phone}/recent`
- Retorna tarefas recentes com contagem total do período
- Query params: `limit`, `days`

#### `GET /agentwpp/ideas/{phone}/recent`
- Retorna ideias recentes com contagem total do período
- Query params: `limit`, `days`

#### `POST /agentwpp/tasks/quick`
- Cria tarefas rápidas sem data
- Body: `{ phone, name, description? }`

### Novos Métodos no Service

#### `getRecentFinances(phone, limit, days)`
- Busca transações recentes ordenadas por data
- Calcula total de transações no período especificado
- Retorna dados formatados para WhatsApp

#### `getRecentTasks(phone, limit, days)`
- Busca tarefas recentes ordenadas por criação
- Calcula total de tarefas no período especificado
- Inclui status de conclusão

#### `getRecentIdeas(phone, limit, days)`
- Busca ideias recentes ordenadas por criação
- Calcula total de ideias no período especificado
- Inclui status de favorito

#### `createQuickTask(createTaskDto)`
- Cria tarefas do tipo 'task' sem data
- Simplificado para uso em conversas

## Estrutura de Resposta

### Formato de Resposta Recente
```json
{
  "recent_transactions": [
    {
      "id": 1,
      "type": "expense",
      "amount": "50.00",
      "description": "Almoço",
      "category_name": "Alimentação",
      "date": "2025-01-15T12:00:00.000Z"
    }
  ],
  "total_in_period": 5,
  "period_days": 1
}
```

### Formato de Resposta Tarefa Rápida
```json
{
  "id": 123,
  "task_type": "task",
  "name": "Comprar leite",
  "description": "Comprar leite no supermercado",
  "task_date": null,
  "completed_at": null,
  "created_at": "2025-01-15T10:30:00.000Z"
}
```

## Benefícios para Conversas WhatsApp

### 1. **Consultas Contextuais**
- Usuários podem perguntar "Quanto gastei hoje?" e receber resumo + detalhes
- Possibilidade de especificar janelas de tempo personalizadas

### 2. **Criação Simplificada**
- Tarefas rápidas sem necessidade de especificar data
- Compromissos específicos com data e hora
- Fluxo mais natural para conversas

### 3. **Informações Resumidas**
- Cada consulta retorna tanto os itens específicos quanto totais
- Facilita geração de respostas informativas

### 4. **Compatibilidade com N8N**
- Todas as ferramentas têm descrições claras dos campos
- Parâmetros opcionais bem definidos
- Tratamento robusto de erros

## Casos de Uso Práticos

### Finanças
- "Me mostre os últimos 10 gastos de hoje"
- "Quantas transações fiz esta semana?"
- "Qual foi meu último gasto?"

### Tarefas
- "Criar tarefa: Ligar para o dentista"
- "Criar compromisso: Reunião amanhã às 15h"
- "Quais são as minhas últimas 5 tarefas?"

### Ideias
- "Me mostre as últimas 3 ideias que tive"
- "Quantas ideias criei hoje?"
- "Qual foi minha última ideia?"

## Tratamento de Erros

Todas as ferramentas utilizam o padrão `executeWithErrorHandling` que:
- Captura e formata erros consistentemente
- Fornece contexto específico para cada ferramenta
- Registra logs detalhados para depuração
- Retorna respostas padronizadas para o N8N

## Configuração

As ferramentas utilizam as mesmas configurações existentes:
- `API_URL`: URL da API backend
- `N8N_API_KEY`: Chave de API para autenticação
- Headers padrão para todas as requisições

## Conclusão

Essas melhorias tornam o servidor MCP mais adequado para conversas WhatsApp naturais, oferecendo:
- Consultas rápidas e contextuais
- Criação simplificada de conteúdo
- Informações resumidas e detalhadas
- Compatibilidade total com N8N
- Tratamento robusto de erros

O sistema agora está pronto para suportar interações WhatsApp mais intuitivas e eficientes.
