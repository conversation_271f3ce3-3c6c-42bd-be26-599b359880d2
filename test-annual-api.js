// Script para testar a API de resumo anual
// Execute com: node test-annual-api.js

// Usando fetch nativo do Node.js (v18+)

const API_URL = 'http://localhost:3000';
const TEST_USER_ID = 5;

// Headers para modo desenvolvimento
const headers = {
  'Content-Type': 'application/json',
  'X-Dev-Mode': 'true',
  'X-Dev-User-Id': TEST_USER_ID.toString(),
  'X-Device-UUID': 'test-device-uuid'
};

async function testAnnualSummary() {
  console.log('🧪 Testando API de Resumo Anual...\n');

  try {
    // Teste 1: Buscar resumo anual para 2025
    console.log('📊 Teste 1: Buscando resumo anual para 2025...');
    const summaryResponse = await fetch(`${API_URL}/finances/annual-summary?year=2025`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${summaryResponse.status} ${summaryResponse.statusText}`);
    
    if (summaryResponse.ok) {
      const summaryData = await summaryResponse.json();
      console.log('✅ Resumo anual obtido com sucesso:');
      console.log('📊 Estrutura dos dados:');
      console.log('- Year:', summaryData.year);
      console.log('- Monthly Data:', summaryData.monthlyData?.length || 0, 'meses');
      console.log('- Totals:', !!summaryData.totals);
      console.log('- Averages:', !!summaryData.averages);
      console.log('- Metrics:', !!summaryData.metrics);
      console.log('- Highlights:', !!summaryData.highlights);

      // Validar estrutura detalhada
      console.log('\n🔍 Validação detalhada:');

      if (summaryData.totals) {
        console.log('✅ Totals:', {
          income: summaryData.totals.income,
          expenses: summaryData.totals.expenses,
          savings: summaryData.totals.savings,
          balance: summaryData.totals.balance
        });
      } else {
        console.log('❌ Totals: AUSENTE');
      }

      if (summaryData.averages) {
        console.log('✅ Averages:', {
          monthlyIncome: summaryData.averages.monthlyIncome,
          monthlyExpenses: summaryData.averages.monthlyExpenses,
          monthlySavings: summaryData.averages.monthlySavings
        });
      } else {
        console.log('❌ Averages: AUSENTE');
      }

      if (summaryData.metrics) {
        console.log('✅ Metrics:', {
          savingsRate: summaryData.metrics.savingsRate,
          goalAmount: summaryData.metrics.goalAmount,
          goalProgress: summaryData.metrics.goalProgress
        });
      } else {
        console.log('❌ Metrics: AUSENTE');
      }

      if (summaryData.highlights) {
        console.log('✅ Highlights:', {
          bestSavingsMonth: summaryData.highlights.bestSavingsMonth,
          worstSavingsMonth: summaryData.highlights.worstSavingsMonth,
          highestIncomeMonth: summaryData.highlights.highestIncomeMonth,
          highestExpenseMonth: summaryData.highlights.highestExpenseMonth
        });
      } else {
        console.log('❌ Highlights: AUSENTE');
      }

      // Verificar se há dados mensais
      if (summaryData.monthlyData && summaryData.monthlyData.length > 0) {
        console.log('✅ Dados mensais:', summaryData.monthlyData.length, 'meses');
        console.log('📅 Primeiro mês:', summaryData.monthlyData[0]);
        console.log('📅 Último mês:', summaryData.monthlyData[summaryData.monthlyData.length - 1]);
      } else {
        console.log('❌ Dados mensais: AUSENTES');
      }
    } else {
      const errorData = await summaryResponse.text();
      console.log('❌ Erro ao buscar resumo anual:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 2: Buscar gastos por categoria para 2025
    console.log('🎯 Teste 2: Buscando gastos por categoria para 2025...');
    const categoryResponse = await fetch(`${API_URL}/finances/expenses-by-category?startDate=2025-01-01&endDate=2025-12-31`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${categoryResponse.status} ${categoryResponse.statusText}`);

    if (categoryResponse.ok) {
      const categoryData = await categoryResponse.json();
      console.log('✅ Gastos por categoria obtidos com sucesso:');
      console.log('📊 Total de categorias:', categoryData.length);
      categoryData.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.category}: R$ ${cat.amount} (${cat.count} transações)`);
      });
    } else {
      const errorData = await categoryResponse.text();
      console.log('❌ Erro ao buscar gastos por categoria:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 3: Buscar todas as categorias
    console.log('🎯 Teste 3: Buscando todas as categorias...');
    const allCategoriesResponse = await fetch(`${API_URL}/finances/categories`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${allCategoriesResponse.status} ${allCategoriesResponse.statusText}`);

    if (allCategoriesResponse.ok) {
      const allCategoriesData = await allCategoriesResponse.json();
      console.log('✅ Categorias obtidas com sucesso:');
      console.log('📊 Total de categorias:', allCategoriesData.length);
      allCategoriesData.forEach((cat, index) => {
        console.log(`${index + 1}. ID: ${cat.id}, Nome: ${cat.name}, Tipo: ${cat.transaction_type}, User: ${cat.user_id}`);
      });
    } else {
      const errorData = await allCategoriesResponse.text();
      console.log('❌ Erro ao buscar categorias:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 5: Buscar todas as transações para verificar category_id
    console.log('🎯 Teste 5: Buscando transações para verificar category_id...');
    const transactionsResponse = await fetch(`${API_URL}/finances?limit=10`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${transactionsResponse.status} ${transactionsResponse.statusText}`);

    if (transactionsResponse.ok) {
      const transactionsData = await transactionsResponse.json();
      console.log('✅ Transações obtidas com sucesso:');
      console.log('📊 Total de transações na página:', transactionsData.finances?.length || 0);

      if (transactionsData.finances && transactionsData.finances.length > 0) {
        console.log('🔍 Primeiras 5 transações:');
        transactionsData.finances.slice(0, 5).forEach((transaction, index) => {
          console.log(`${index + 1}. ID: ${transaction.id}, Tipo: ${transaction.transaction_type}, Category ID: ${transaction.category_id}, Category Name: "${transaction.category_name}", Amount: ${transaction.amount}`);
        });
      }
    } else {
      const errorData = await transactionsResponse.text();
      console.log('❌ Erro ao buscar transações:');
      console.log(errorData);
    }
      method: 'GET',
      headers
    });

    console.log(`Status: ${goalResponse.status} ${goalResponse.statusText}`);
    
    if (goalResponse.ok) {
      const goalData = await goalResponse.json();
      console.log('✅ Meta anual obtida com sucesso:');
      console.log(JSON.stringify(goalData, null, 2));
    } else {
      const errorData = await goalResponse.text();
      console.log('❌ Erro ao buscar meta anual:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 3: Criar/Atualizar meta anual
    console.log('💰 Teste 3: Criando/Atualizando meta anual...');
    const createGoalResponse = await fetch(`${API_URL}/finances/annual-goal`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        goalAmount: 12000,
        year: 2024
      })
    });

    console.log(`Status: ${createGoalResponse.status} ${createGoalResponse.statusText}`);
    
    if (createGoalResponse.ok) {
      const createGoalData = await createGoalResponse.json();
      console.log('✅ Meta anual criada/atualizada com sucesso:');
      console.log(JSON.stringify(createGoalData, null, 2));
    } else {
      const errorData = await createGoalResponse.text();
      console.log('❌ Erro ao criar/atualizar meta anual:');
      console.log(errorData);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Teste 4: Buscar todas as finanças
    console.log('💳 Teste 4: Buscando todas as transações...');
    const financesResponse = await fetch(`${API_URL}/finances`, {
      method: 'GET',
      headers
    });

    console.log(`Status: ${financesResponse.status} ${financesResponse.statusText}`);
    
    if (financesResponse.ok) {
      const financesData = await financesResponse.json();
      console.log('✅ Transações obtidas com sucesso:');
      console.log(`Total de transações: ${financesData.total}`);
      console.log(`Transações na página: ${financesData.finances?.length || 0}`);
      
      if (financesData.finances && financesData.finances.length > 0) {
        console.log('Primeira transação:');
        console.log(JSON.stringify(financesData.finances[0], null, 2));
      }
    } else {
      const errorData = await financesResponse.text();
      console.log('❌ Erro ao buscar transações:');
      console.log(errorData);
    }

  } catch (error) {
    console.error('❌ Erro durante os testes:', error.message);
  }
}

// Executar os testes
testAnnualSummary();
