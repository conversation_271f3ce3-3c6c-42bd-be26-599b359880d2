import { useQuery } from '@tanstack/react-query';
import { authenticatedApi } from '../lib/api';
import { DashboardResponseDto } from '../types/api';

export const useDashboardData = () => {
  return useQuery({
    queryKey: ['dashboard'],
    queryFn: async (): Promise<DashboardResponseDto> => {
      const response = await authenticatedApi.get('dashboard');
      const responseData = await response.json();
      
      // Handle nested response structure: response.data.data or response.data or response
      return responseData.data?.data || responseData.data || responseData;
    },
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos
    refetchOnWindowFocus: false, // Evita re-fetch ao focar na janela
    retry: (failureCount, error: any) => {
      // Não tentar novamente para erros de autenticação
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
  });
};
