import { IsNotEmpty, IsString, IsBoolean, IsEnum, IsOptional } from 'class-validator';

export class CreateWhatsAppIntegrationDto {
  // Phone não é mais obrigatório na criação, será gerado um código de ativação
}

export class ValidateWhatsAppIntegrationDto {
  @IsNotEmpty()
  @IsString()
  activation_code: string;

  @IsNotEmpty()
  @IsString()
  phone: string;
}

export class UpdateWhatsAppIntegrationDto {
  @IsOptional()
  @IsEnum(['pending', 'active', 'inactive'])
  status?: 'pending' | 'active' | 'inactive';

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsBoolean()
  is_validated?: boolean;
}

export class WhatsAppIntegrationResponseDto {
  id: number;
  status: 'pending' | 'active' | 'inactive';
  phone?: string | null;
  is_validated: boolean;
  activation_code?: string | null;
  user_id: number;
  created_at: Date;
  updated_at: Date;
}
