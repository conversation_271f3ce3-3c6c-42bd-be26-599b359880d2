-- Script de inicialização do banco de dados Dupli
-- <PERSON>ste script cria todas as tabelas necessárias para o funcionamento da aplicação

-- Tabela de usuários
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255),
  phone VARCHAR(20),
  timezone VARCHAR(50) NOT NULL DEFAULT 'America/Sao_Paulo',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL
);

-- Tabela de tokens de refresh
CREATE TABLE IF NOT EXISTS refresh_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  refresh_token VARCHAR(500) NOT NULL,
  device_uuid VARCHAR(255) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  revoked BO<PERSON>EA<PERSON> DEFAULT FALSE,
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabela de metas anuais de economia
CREATE TABLE IF NOT EXISTS config_annual_savings_goal (
  id INT AUTO_INCREMENT PRIMARY KEY,
  year INT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  UNIQUE KEY unique_user_year (user_id, year)
);

-- Tabela de categorias de tarefas
CREATE TABLE IF NOT EXISTS tasks_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabela de tarefas
CREATE TABLE IF NOT EXISTS tasks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  task_type ENUM('appointment', 'task') NOT NULL,
  category_id INT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  task_date TIMESTAMP NULL,
  user_id INT NOT NULL,
  completed_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES tasks_categories(id) ON DELETE SET NULL
);

-- Tabela de categorias financeiras
CREATE TABLE IF NOT EXISTS finances_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  transaction_type ENUM('income', 'expense') NOT NULL,
  color VARCHAR(7) DEFAULT '#B4EB00',
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabela de transações financeiras
CREATE TABLE IF NOT EXISTS finances (
  id INT AUTO_INCREMENT PRIMARY KEY,
  transaction_type ENUM('income', 'expense') NOT NULL,
  category_id INT,
  is_saving BOOLEAN DEFAULT FALSE,
  description TEXT,
  amount DECIMAL(10,2) NOT NULL,
  transaction_date TIMESTAMP NOT NULL,
  user_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES finances_categories(id) ON DELETE SET NULL
);

-- Tabela de categorias de ideias
CREATE TABLE IF NOT EXISTS ideas_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  user_id INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Tabela de ideias
CREATE TABLE IF NOT EXISTS ideas (
  id INT AUTO_INCREMENT PRIMARY KEY,
  category_id INT,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  content TEXT,
  is_favorite BOOLEAN DEFAULT FALSE,
  user_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (category_id) REFERENCES ideas_categories(id) ON DELETE SET NULL
);

-- Tabela de integração WhatsApp
CREATE TABLE IF NOT EXISTS integrations_whatsapp (
  id INT AUTO_INCREMENT PRIMARY KEY,
  status ENUM('pending', 'active', 'inactive') NOT NULL,
  phone VARCHAR(20) NULL,
  is_validated BOOLEAN DEFAULT FALSE,
  activation_code VARCHAR(255) NULL,
  user_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_device_uuid ON refresh_tokens(device_uuid);
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_user_id ON refresh_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_tasks_task_date ON tasks(task_date);
CREATE INDEX IF NOT EXISTS idx_finances_user_id ON finances(user_id);
CREATE INDEX IF NOT EXISTS idx_finances_transaction_date ON finances(transaction_date);
CREATE INDEX IF NOT EXISTS idx_ideas_user_id ON ideas(user_id);

-- Inserir categorias financeiras padrão (opcional)
INSERT IGNORE INTO finances_categories (name, transaction_type, color, user_id) VALUES
('Alimentação', 'expense', '#FF6B6B', NULL),
('Transporte', 'expense', '#4ECDC4', NULL),
('Moradia', 'expense', '#45B7D1', NULL),
('Saúde', 'expense', '#96CEB4', NULL),
('Educação', 'expense', '#FFEAA7', NULL),
('Lazer', 'expense', '#DDA0DD', NULL),
('Salário', 'income', '#98D8C8', NULL),
('Freelance', 'income', '#F7DC6F', NULL),
('Investimentos', 'income', '#BB8FCE', NULL);
