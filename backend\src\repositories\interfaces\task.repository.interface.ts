import { IBaseRepository } from '../../common/interfaces/base-repository.interface';
import { CreateTaskDto } from '../../tasks/dto/create-task.dto';
import { UpdateTaskDto } from '../../tasks/dto/update-task.dto';
import { TaskResponseDto } from '../../tasks/dto/task-response.dto';

export interface ITaskRepository extends IBaseRepository<TaskResponseDto, CreateTaskDto, UpdateTaskDto> {
  complete(id: number, userId: number, userTimezone: string): Promise<TaskResponseDto>;
}