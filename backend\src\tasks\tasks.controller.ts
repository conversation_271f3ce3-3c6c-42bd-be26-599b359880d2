import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
  ParseIntPipe,
  HttpException,
  HttpStatus,
  Logger,
  Put
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { DevAwareAuthGuard } from '../auth/dev-auth.guard';
import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { TaskResponseDto, TaskListResponseDto } from './dto/task-response.dto';
import { CreateTaskCategoryDto } from './dto/create-task-category.dto';
import { UpdateTaskCategoryDto } from './dto/update-task-category.dto';
import { TaskCategoryResponseDto } from './dto/task-category-response.dto';

@Controller('tasks')
@UseGuards(DevAwareAuthGuard)
export class TasksController {
  private readonly logger = new Logger(TasksController.name);

  constructor(private readonly tasksService: TasksService) {}

  @Post()
  async create(@Body() createTaskDto: CreateTaskDto, @Request() req): Promise<TaskResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.create(createTaskDto, userId, userTimezone);
    } catch (error) {
      this.logger.error('Erro ao criar tarefa:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get()
  async findAll(
    @Request() req,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string
  ): Promise<TaskListResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.findAll(
        userId,
        userTimezone,
        Number(page),
        Number(limit),
        startDate,
        endDate
      );
    } catch (error) {
      this.logger.error('Erro ao listar tarefas:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  // Endpoints para categorias de tarefas (devem vir antes das rotas com parâmetros)
  @Post('categories')
  async createCategory(@Body() createCategoryDto: CreateTaskCategoryDto, @Request() req): Promise<TaskCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.createCategory(createCategoryDto, userId);
    } catch (error) {
      this.logger.error('Erro ao criar categoria:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories')
  async findAllCategories(@Request() req): Promise<TaskCategoryResponseDto[]> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.findAllCategories(userId);
    } catch (error) {
      this.logger.error('Erro ao listar categorias:', error);
      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('categories/:id')
  async findOneCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<TaskCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.findOneCategory(id, userId);
    } catch (error) {
      this.logger.error(`Erro ao buscar categoria ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('categories/:id')
  async updateCategory(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCategoryDto: UpdateTaskCategoryDto,
    @Request() req
  ): Promise<TaskCategoryResponseDto> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.updateCategory(id, updateCategoryDto, userId);
    } catch (error) {
      this.logger.error(`Erro ao atualizar categoria ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('categories/:id')
  async removeCategory(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.tasksService.removeCategory(id, userId);
      return { message: 'Categoria removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover categoria ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      if (error.message.includes('está sendo usada')) {
        throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<TaskResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.findOne(id, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao buscar tarefa ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTaskDto: UpdateTaskDto,
    @Request() req
  ): Promise<TaskResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.update(id, updateTaskDto, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao atualizar tarefa ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete(':id')
  async remove(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<{ message: string }> {
    try {
      const userId = req.user?.userId;

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      await this.tasksService.remove(id, userId);
      return { message: 'Tarefa removida com sucesso' };
    } catch (error) {
      this.logger.error(`Erro ao remover tarefa ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Patch(':id/complete')
  async complete(@Param('id', ParseIntPipe) id: number, @Request() req): Promise<TaskResponseDto> {
    try {
      const userId = req.user?.userId;
      const userTimezone = await this.getUserTimezone(userId);

      if (!userId) {
        throw new HttpException('ID do usuário não encontrado', HttpStatus.UNAUTHORIZED);
      }

      return await this.tasksService.complete(id, userId, userTimezone);
    } catch (error) {
      this.logger.error(`Erro ao completar tarefa ${id}:`, error);

      if (error.message.includes('não encontrada')) {
        throw new HttpException(error.message, HttpStatus.NOT_FOUND);
      }

      throw new HttpException(
        error.message || 'Erro interno do servidor',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private async getUserTimezone(userId: number): Promise<string> {
    // Implementar busca do timezone do usuário
    // Por enquanto, retornar um padrão
    return 'America/Sao_Paulo';
  }
}
