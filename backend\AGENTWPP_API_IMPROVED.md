# API AgentWPP - Documentação Melhorada

## Visão Geral

A API AgentWPP foi aprimorada com melhorias significativas em estrutura, validação, tratamento de erros e padronização de respostas.

## Melhorias Implementadas

### 1. **Estrutura de Resposta Padronizada**
- **SuccessResponseDto**: Resposta padrão para operações bem-sucedidas
- **ErrorResponseDto**: Resposta padrão para erros
- **PaginatedResponseDto**: Resposta paginada genérica
- **PaginatedFinancialResponseDto**: Resposta paginada com resumo financeiro

### 2. **Validações Aprimoradas**
- Validação automática de telefones (normalização)
- Validações específicas para campos monetários
- Transformação automática de dados
- Mensagens de erro em português

### 3. **Logs Estruturados**
- Log de todas as requisições com timing
- Log estruturado com metadados
- Sanitização automática de dados sensíveis
- Logs de erro detalhados

### 4. **Tratamento de Erros Global**
- Filtro global de exceções
- Respostas de erro padronizadas
- Stack trace apenas em desenvolvimento
- Logs automáticos de erros

### 5. **Cálculos Financeiros**
- Resumo financeiro automático nas listagens
- Utilitários para cálculos monetários
- Validação de valores monetários
- Formatação de moeda

## Estrutura de Resposta

### Sucesso
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "timestamp": "2025-01-29T14:00:00.000Z"
}
```

### Erro
```json
{
  "success": false,
  "message": "Error message",
  "error": "Error details",
  "timestamp": "2025-01-29T14:00:00.000Z",
  "statusCode": 400
}
```

### Resposta Paginada
```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 50,
  "totalPages": 2,
  "hasNext": true,
  "hasPrevious": false
}
```

### Resposta Financeira Paginada
```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 50,
  "totalPages": 2,
  "hasNext": true,
  "hasPrevious": false,
  "summary": {
    "totalAmount": 5000.00,
    "totalIncome": 8000.00,
    "totalExpenses": 3000.00,
    "balance": 5000.00
  }
}
```

## Endpoints

### Autenticação
- **Header obrigatório**: `Authorization: Bearer {api-key}`
- **Guard**: ApiKeyGuard aplicado globalmente

### 1. Verificação de Integração
```
GET /agentwpp/check-integration/{phone}
```

### 2. Validação de Número
```
POST /agentwpp/validate
Body: { "activation_code": "123456", "phone": "5511999999999" }
```

### 3. Dashboard
```
GET /agentwpp/dashboard/{phone}
```

### 4. Tarefas

#### Listar Tarefas
```
GET /agentwpp/tasks/{phone}?page=1&limit=50
```
**Resposta**: Paginação padrão com tarefas

#### Criar Tarefa
```
POST /agentwpp/tasks
Body: {
  "phone": "5511999999999",
  "task_type": "task|appointment",
  "category_id": 1,
  "name": "Minha tarefa",
  "description": "Descrição da tarefa",
  "task_date": "2025-01-29T14:00:00.000Z"
}
```

#### Tarefa Rápida
```
POST /agentwpp/tasks/quick
Body: {
  "phone": "5511999999999",
  "name": "Tarefa rápida",
  "description": "Descrição opcional"
}
```

#### Tarefas Recentes
```
GET /agentwpp/tasks/{phone}/recent?limit=10&days=7
```

#### Categorias de Tarefas
```
GET /agentwpp/tasks/{phone}/categories
```

### 5. Finanças

#### Listar Finanças
```
GET /agentwpp/finances/{phone}?page=1&limit=50
```
**Resposta**: Paginação com resumo financeiro incluído

#### Criar Transação Financeira
```
POST /agentwpp/finances
Body: {
  "phone": "5511999999999",
  "transaction_type": "income|expense",
  "category_id": 1,
  "is_saving": false,
  "description": "Descrição da transação",
  "amount": "100.50",
  "transaction_date": "2025-01-29T14:00:00.000Z"
}
```

#### Resumo Financeiro
```
GET /agentwpp/finances/{phone}/summary?startDate=2025-01-01&endDate=2025-01-31
```

#### Transações Recentes
```
GET /agentwpp/finances/{phone}/recent?limit=10&days=7
```

#### Categorias Financeiras
```
GET /agentwpp/finances/{phone}/categories
```

### 6. Ideias

#### Listar Ideias
```
GET /agentwpp/ideas/{phone}?page=1&limit=50
```

#### Criar Ideia
```
POST /agentwpp/ideas
Body: {
  "phone": "5511999999999",
  "category_id": 1,
  "name": "Minha ideia",
  "description": "Descrição da ideia",
  "content": "Conteúdo detalhado",
  "is_favorite": false
}
```

#### Ideias Recentes
```
GET /agentwpp/ideas/{phone}/recent?limit=10&days=7
```

#### Alternar Favorito
```
PATCH /agentwpp/ideas/{phone}/{id}/favorite
```

#### Categorias de Ideias
```
GET /agentwpp/ideas/{phone}/categories
```

## Validações

### Telefone
- Obrigatório em todos os endpoints
- Normalização automática (remove espaços e caracteres especiais)
- Mantém apenas números e símbolo `+`

### Valores Monetários
- Formato string com até 2 casas decimais
- Validação automática de valores válidos
- Conversão automática para cálculos

### Datas
- Formato ISO 8601 (ex: `2025-01-29T14:00:00.000Z`)
- Validação automática de formato

## Tratamento de Erros

### Códigos de Status
- **200**: Sucesso
- **201**: Criado com sucesso
- **400**: Erro de validação ou dados inválidos
- **401**: Não autorizado (API key inválida)
- **403**: Proibido (telefone sem integração ativa)
- **404**: Recurso não encontrado
- **500**: Erro interno do servidor

### Logs
- Todas as requisições são logadas automaticamente
- Erros incluem stack trace para debugging
- Dados sensíveis são automaticamente sanitizados
- Logs estruturados em formato JSON

## Utilitários Financeiros

### FinancialUtils
- `calculateSummary()`: Calcula resumo de transações
- `formatCurrency()`: Formata valores para exibição
- `parseDecimal()`: Converte string para decimal
- `isValidAmount()`: Valida valores monetários
- `calculatePercentageChange()`: Calcula percentual de mudança

## Desenvolvimento

### Instalação
```bash
npm install
```

### Executar
```bash
npm run start:dev
```

### Build
```bash
npm run build
```

### Testes
```bash
npm run test
```

## Configuração

### Variáveis de Ambiente
- `PORT`: Porta do servidor (padrão: 3000)
- `CORS_ORIGIN`: Origem permitida para CORS
- Outras configurações de banco de dados conforme necessário

### Middleware
- CORS configurado automaticamente
- Validação global ativa
- Interceptores de resposta ativos
- Filtros de exceção globais

## Consumo no N8N

A API foi projetada para ser facilmente consumida no N8N:

1. **Autenticação**: Configure o header Authorization com a API key
2. **Responses**: Todas as respostas seguem formato padronizado
3. **Erros**: Tratamento consistente facilita debugging
4. **Paginação**: Formato padrão para todas as listagens
5. **Logs**: Monitoramento completo das operações

### Exemplo de Configuração no N8N
```json
{
  "method": "GET",
  "url": "http://localhost:3000/agentwpp/tasks/5511999999999",
  "headers": {
    "Authorization": "Bearer sua-api-key-aqui",
    "Content-Type": "application/json"
  },
  "parameters": {
    "page": 1,
    "limit": 50
  }
}
```
